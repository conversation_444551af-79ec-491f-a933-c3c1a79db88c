[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts": "74", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-pricing/page.tsx": "75", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx": "76", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx": "77", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx": "78", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx": "79", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx": "80", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts": "81", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx": "82"}, {"size": 12560, "mtime": 1750914757080, "results": "83", "hashOfConfig": "84"}, {"size": 17221, "mtime": 1751039272116, "results": "85", "hashOfConfig": "84"}, {"size": 15666, "mtime": 1751039201443, "results": "86", "hashOfConfig": "84"}, {"size": 5321, "mtime": 1750906802986, "results": "87", "hashOfConfig": "84"}, {"size": 1926, "mtime": 1751014639961, "results": "88", "hashOfConfig": "84"}, {"size": 2073, "mtime": 1751038886395, "results": "89", "hashOfConfig": "84"}, {"size": 3121, "mtime": 1751014815428, "results": "90", "hashOfConfig": "84"}, {"size": 171, "mtime": 1750921851894, "results": "91", "hashOfConfig": "84"}, {"size": 3714, "mtime": 1750921931408, "results": "92", "hashOfConfig": "84"}, {"size": 4489, "mtime": 1750930430193, "results": "93", "hashOfConfig": "84"}, {"size": 2576, "mtime": 1751039063089, "results": "94", "hashOfConfig": "84"}, {"size": 4403, "mtime": 1750924179468, "results": "95", "hashOfConfig": "84"}, {"size": 5614, "mtime": 1750951694652, "results": "96", "hashOfConfig": "84"}, {"size": 7403, "mtime": 1751038610858, "results": "97", "hashOfConfig": "84"}, {"size": 5019, "mtime": 1751038932824, "results": "98", "hashOfConfig": "84"}, {"size": 5538, "mtime": 1750952541605, "results": "99", "hashOfConfig": "84"}, {"size": 2484, "mtime": 1750938669998, "results": "100", "hashOfConfig": "84"}, {"size": 14489, "mtime": 1751037810184, "results": "101", "hashOfConfig": "84"}, {"size": 14183, "mtime": 1751039428849, "results": "102", "hashOfConfig": "84"}, {"size": 2545, "mtime": 1751032793319, "results": "103", "hashOfConfig": "84"}, {"size": 11769, "mtime": 1751038975138, "results": "104", "hashOfConfig": "84"}, {"size": 7642, "mtime": 1750951120332, "results": "105", "hashOfConfig": "84"}, {"size": 10389, "mtime": 1750945315721, "results": "106", "hashOfConfig": "84"}, {"size": 20955, "mtime": 1751014907413, "results": "107", "hashOfConfig": "84"}, {"size": 21004, "mtime": 1750945519465, "results": "108", "hashOfConfig": "84"}, {"size": 16243, "mtime": 1751018388834, "results": "109", "hashOfConfig": "84"}, {"size": 4543, "mtime": 1750930937103, "results": "110", "hashOfConfig": "84"}, {"size": 5300, "mtime": 1751039006848, "results": "111", "hashOfConfig": "84"}, {"size": 9895, "mtime": 1751038797327, "results": "112", "hashOfConfig": "84"}, {"size": 1425, "mtime": 1750903550616, "results": "113", "hashOfConfig": "84"}, {"size": 845, "mtime": 1750908285683, "results": "114", "hashOfConfig": "84"}, {"size": 3162, "mtime": 1751023074001, "results": "115", "hashOfConfig": "84"}, {"size": 505, "mtime": 1750908273441, "results": "116", "hashOfConfig": "84"}, {"size": 863, "mtime": 1750908296528, "results": "117", "hashOfConfig": "84"}, {"size": 5768, "mtime": 1750942157899, "results": "118", "hashOfConfig": "84"}, {"size": 4494, "mtime": 1751023171715, "results": "119", "hashOfConfig": "84"}, {"size": 9109, "mtime": 1750930558601, "results": "120", "hashOfConfig": "84"}, {"size": 6661, "mtime": 1750945557905, "results": "121", "hashOfConfig": "84"}, {"size": 4438, "mtime": 1750923424688, "results": "122", "hashOfConfig": "84"}, {"size": 867, "mtime": 1750922283437, "results": "123", "hashOfConfig": "84"}, {"size": 362, "mtime": 1750922147686, "results": "124", "hashOfConfig": "84"}, {"size": 8935, "mtime": 1750924218629, "results": "125", "hashOfConfig": "84"}, {"size": 3198, "mtime": 1750951009317, "results": "126", "hashOfConfig": "84"}, {"size": 2449, "mtime": 1750942881883, "results": "127", "hashOfConfig": "84"}, {"size": 7116, "mtime": 1751038853366, "results": "128", "hashOfConfig": "84"}, {"size": 5059, "mtime": 1750930729612, "results": "129", "hashOfConfig": "84"}, {"size": 921, "mtime": 1750903252798, "results": "130", "hashOfConfig": "84"}, {"size": 6818, "mtime": 1750903357994, "results": "131", "hashOfConfig": "84"}, {"size": 1667, "mtime": 1750903308052, "results": "132", "hashOfConfig": "84"}, {"size": 2141, "mtime": 1750921803605, "results": "133", "hashOfConfig": "84"}, {"size": 5050, "mtime": 1751038533558, "results": "134", "hashOfConfig": "84"}, {"size": 3406, "mtime": 1750921782108, "results": "135", "hashOfConfig": "84"}, {"size": 720, "mtime": 1750903327281, "results": "136", "hashOfConfig": "84"}, {"size": 3866, "mtime": 1750984404444, "results": "137", "hashOfConfig": "84"}, {"size": 2238, "mtime": 1750995712168, "results": "138", "hashOfConfig": "84"}, {"size": 4057, "mtime": 1751018497440, "results": "139", "hashOfConfig": "84"}, {"size": 5242, "mtime": 1751013821668, "results": "140", "hashOfConfig": "84"}, {"size": 1022, "mtime": 1750984456438, "results": "141", "hashOfConfig": "84"}, {"size": 11976, "mtime": 1751038689677, "results": "142", "hashOfConfig": "84"}, {"size": 2237, "mtime": 1750949131424, "results": "143", "hashOfConfig": "84"}, {"size": 3202, "mtime": 1750953105864, "results": "144", "hashOfConfig": "84"}, {"size": 8048, "mtime": 1751018710311, "results": "145", "hashOfConfig": "84"}, {"size": 6764, "mtime": 1751005731451, "results": "146", "hashOfConfig": "84"}, {"size": 4621, "mtime": 1751005744888, "results": "147", "hashOfConfig": "84"}, {"size": 12431, "mtime": 1751004268664, "results": "148", "hashOfConfig": "84"}, {"size": 3885, "mtime": 1751018851458, "results": "149", "hashOfConfig": "84"}, {"size": 7687, "mtime": 1751017905894, "results": "150", "hashOfConfig": "84"}, {"size": 3527, "mtime": 1751018284048, "results": "151", "hashOfConfig": "84"}, {"size": 3985, "mtime": 1751017840303, "results": "152", "hashOfConfig": "84"}, {"size": 3989, "mtime": 1750984256539, "results": "153", "hashOfConfig": "84"}, {"size": 23317, "mtime": 1751038760433, "results": "154", "hashOfConfig": "84"}, {"size": 3519, "mtime": 1751038552644, "results": "155", "hashOfConfig": "84"}, {"size": 4413, "mtime": 1751019030952, "results": "156", "hashOfConfig": "84"}, {"size": 2042, "mtime": 1751039100757, "results": "157", "hashOfConfig": "84"}, {"size": 6972, "mtime": 1751018762448, "results": "158", "hashOfConfig": "84"}, {"size": 5275, "mtime": 1751023043127, "results": "159", "hashOfConfig": "84"}, {"size": 2605, "mtime": 1751019665417, "results": "160", "hashOfConfig": "84"}, {"size": 9808, "mtime": 1751037810178, "results": "161", "hashOfConfig": "84"}, {"size": 5433, "mtime": 1751023279983, "results": "162", "hashOfConfig": "84"}, {"size": 3048, "mtime": 1751023262678, "results": "163", "hashOfConfig": "84"}, {"size": 3275, "mtime": 1751019689085, "results": "164", "hashOfConfig": "84"}, {"size": 8836, "mtime": 1751037810161, "results": "165", "hashOfConfig": "84"}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ifueu7", {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx", ["412", "413", "414", "415", "416", "417", "418", "419", "420", "421"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx", ["422", "423", "424", "425", "426", "427"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx", ["428", "429", "430", "431", "432", "433"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["434", "435"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["436", "437"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["438"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["439", "440"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["441", "442", "443"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["444"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx", ["445"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx", ["446", "447"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", ["448", "449"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx", ["450", "451"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx", ["452", "453", "454"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx", ["455"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx", ["456", "457", "458", "459", "460", "461"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx", ["462"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx", ["463", "464", "465", "466"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx", ["467", "468", "469", "470", "471", "472", "473", "474", "475", "476"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["477", "478", "479"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["480", "481"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["482", "483", "484", "485"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["486"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["487", "488", "489", "490"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["491"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["492"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["493"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["494", "495"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["496", "497", "498", "499"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx", ["500", "501", "502", "503", "504"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx", ["505", "506"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx", ["507", "508"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx", ["509", "510", "511"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx", ["512"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["513"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx", ["514"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts", ["515"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-pricing/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx", ["516", "517", "518"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx", ["519", "520", "521", "522", "523"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx", ["524"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx", ["525", "526", "527"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx", ["528"], [], {"ruleId": "529", "severity": 2, "message": "530", "line": 11, "column": 3, "nodeType": null, "messageId": "531", "endLine": 11, "endColumn": 8}, {"ruleId": "529", "severity": 2, "message": "532", "line": 16, "column": 3, "nodeType": null, "messageId": "531", "endLine": 16, "endColumn": 8}, {"ruleId": "529", "severity": 2, "message": "533", "line": 17, "column": 3, "nodeType": null, "messageId": "531", "endLine": 17, "endColumn": 11}, {"ruleId": "529", "severity": 2, "message": "534", "line": 20, "column": 3, "nodeType": null, "messageId": "531", "endLine": 20, "endColumn": 7}, {"ruleId": "529", "severity": 2, "message": "535", "line": 25, "column": 7, "nodeType": null, "messageId": "531", "endLine": 25, "endColumn": 21}, {"ruleId": "536", "severity": 1, "message": "537", "line": 48, "column": 6, "nodeType": "538", "endLine": 48, "endColumn": 17, "suggestions": "539"}, {"ruleId": "529", "severity": 2, "message": "540", "line": 62, "column": 14, "nodeType": null, "messageId": "531", "endLine": 62, "endColumn": 17}, {"ruleId": "529", "severity": 2, "message": "541", "line": 69, "column": 9, "nodeType": null, "messageId": "531", "endLine": 69, "endColumn": 19}, {"ruleId": "529", "severity": 2, "message": "542", "line": 78, "column": 9, "nodeType": null, "messageId": "531", "endLine": 78, "endColumn": 24}, {"ruleId": "529", "severity": 2, "message": "543", "line": 91, "column": 9, "nodeType": null, "messageId": "531", "endLine": 91, "endColumn": 27}, {"ruleId": "529", "severity": 2, "message": "544", "line": 17, "column": 3, "nodeType": null, "messageId": "531", "endLine": 17, "endColumn": 17}, {"ruleId": "536", "severity": 1, "message": "545", "line": 61, "column": 6, "nodeType": "538", "endLine": 61, "endColumn": 20, "suggestions": "546"}, {"ruleId": "529", "severity": 2, "message": "540", "line": 78, "column": 14, "nodeType": null, "messageId": "531", "endLine": 78, "endColumn": 17}, {"ruleId": "529", "severity": 2, "message": "540", "line": 111, "column": 14, "nodeType": null, "messageId": "531", "endLine": 111, "endColumn": 17}, {"ruleId": "529", "severity": 2, "message": "540", "line": 137, "column": 14, "nodeType": null, "messageId": "531", "endLine": 137, "endColumn": 17}, {"ruleId": "547", "severity": 1, "message": "548", "line": 304, "column": 27, "nodeType": "549", "endLine": 308, "endColumn": 29}, {"ruleId": "529", "severity": 2, "message": "550", "line": 74, "column": 9, "nodeType": null, "messageId": "531", "endLine": 74, "endColumn": 15}, {"ruleId": "529", "severity": 2, "message": "551", "line": 88, "column": 14, "nodeType": null, "messageId": "531", "endLine": 88, "endColumn": 19}, {"ruleId": "529", "severity": 2, "message": "551", "line": 104, "column": 14, "nodeType": null, "messageId": "531", "endLine": 104, "endColumn": 19}, {"ruleId": "552", "severity": 2, "message": "553", "line": 123, "column": 33, "nodeType": "554", "messageId": "555", "endLine": 123, "endColumn": 36, "suggestions": "556"}, {"ruleId": "547", "severity": 1, "message": "548", "line": 179, "column": 15, "nodeType": "549", "endLine": 183, "endColumn": 17}, {"ruleId": "547", "severity": 1, "message": "548", "line": 273, "column": 19, "nodeType": "549", "endLine": 278, "endColumn": 21}, {"ruleId": "552", "severity": 2, "message": "553", "line": 19, "column": 18, "nodeType": "554", "messageId": "555", "endLine": 19, "endColumn": 21, "suggestions": "557"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 55, "column": 22, "nodeType": "554", "messageId": "555", "endLine": 55, "endColumn": 25, "suggestions": "558"}, {"ruleId": "529", "severity": 2, "message": "559", "line": 8, "column": 27, "nodeType": null, "messageId": "531", "endLine": 8, "endColumn": 34}, {"ruleId": "552", "severity": 2, "message": "553", "line": 96, "column": 23, "nodeType": "554", "messageId": "555", "endLine": 96, "endColumn": 26, "suggestions": "560"}, {"ruleId": "529", "severity": 2, "message": "559", "line": 6, "column": 27, "nodeType": null, "messageId": "531", "endLine": 6, "endColumn": 34}, {"ruleId": "552", "severity": 2, "message": "553", "line": 179, "column": 20, "nodeType": "554", "messageId": "555", "endLine": 179, "endColumn": 23, "suggestions": "561"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 217, "column": 70, "nodeType": "554", "messageId": "555", "endLine": 217, "endColumn": 73, "suggestions": "562"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 22, "column": 18, "nodeType": "554", "messageId": "555", "endLine": 22, "endColumn": 21, "suggestions": "563"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 66, "column": 22, "nodeType": "554", "messageId": "555", "endLine": 66, "endColumn": 25, "suggestions": "564"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 168, "column": 70, "nodeType": "554", "messageId": "555", "endLine": 168, "endColumn": 73, "suggestions": "565"}, {"ruleId": "529", "severity": 2, "message": "551", "line": 56, "column": 14, "nodeType": null, "messageId": "531", "endLine": 56, "endColumn": 19}, {"ruleId": "536", "severity": 1, "message": "566", "line": 54, "column": 6, "nodeType": "538", "endLine": 54, "endColumn": 19, "suggestions": "567"}, {"ruleId": "529", "severity": 2, "message": "535", "line": 22, "column": 7, "nodeType": null, "messageId": "531", "endLine": 22, "endColumn": 21}, {"ruleId": "529", "severity": 2, "message": "540", "line": 109, "column": 14, "nodeType": null, "messageId": "531", "endLine": 109, "endColumn": 17}, {"ruleId": "568", "severity": 2, "message": "569", "line": 198, "column": 13, "nodeType": "549", "endLine": 201, "endColumn": 14}, {"ruleId": "568", "severity": 2, "message": "569", "line": 259, "column": 15, "nodeType": "549", "endLine": 262, "endColumn": 16}, {"ruleId": "536", "severity": 1, "message": "570", "line": 55, "column": 6, "nodeType": "538", "endLine": 55, "endColumn": 49, "suggestions": "571"}, {"ruleId": "529", "severity": 2, "message": "540", "line": 70, "column": 14, "nodeType": null, "messageId": "531", "endLine": 70, "endColumn": 17}, {"ruleId": "529", "severity": 2, "message": "572", "line": 11, "column": 3, "nodeType": null, "messageId": "531", "endLine": 11, "endColumn": 7}, {"ruleId": "529", "severity": 2, "message": "573", "line": 18, "column": 3, "nodeType": null, "messageId": "531", "endLine": 18, "endColumn": 7}, {"ruleId": "547", "severity": 1, "message": "548", "line": 97, "column": 19, "nodeType": "549", "endLine": 101, "endColumn": 21}, {"ruleId": "529", "severity": 2, "message": "551", "line": 104, "column": 14, "nodeType": null, "messageId": "531", "endLine": 104, "endColumn": 19}, {"ruleId": "529", "severity": 2, "message": "574", "line": 12, "column": 3, "nodeType": null, "messageId": "531", "endLine": 12, "endColumn": 7}, {"ruleId": "529", "severity": 2, "message": "575", "line": 15, "column": 3, "nodeType": null, "messageId": "531", "endLine": 15, "endColumn": 6}, {"ruleId": "529", "severity": 2, "message": "540", "line": 94, "column": 14, "nodeType": null, "messageId": "531", "endLine": 94, "endColumn": 17}, {"ruleId": "529", "severity": 2, "message": "540", "line": 111, "column": 14, "nodeType": null, "messageId": "531", "endLine": 111, "endColumn": 17}, {"ruleId": "529", "severity": 2, "message": "540", "line": 128, "column": 14, "nodeType": null, "messageId": "531", "endLine": 128, "endColumn": 17}, {"ruleId": "547", "severity": 1, "message": "548", "line": 197, "column": 23, "nodeType": "549", "endLine": 201, "endColumn": 25}, {"ruleId": "547", "severity": 1, "message": "548", "line": 300, "column": 21, "nodeType": "549", "endLine": 304, "endColumn": 23}, {"ruleId": "529", "severity": 2, "message": "551", "line": 27, "column": 14, "nodeType": null, "messageId": "531", "endLine": 27, "endColumn": 19}, {"ruleId": "529", "severity": 2, "message": "551", "line": 46, "column": 14, "nodeType": null, "messageId": "531", "endLine": 46, "endColumn": 19}, {"ruleId": "552", "severity": 2, "message": "553", "line": 63, "column": 42, "nodeType": "554", "messageId": "555", "endLine": 63, "endColumn": 45, "suggestions": "576"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 64, "column": 42, "nodeType": "554", "messageId": "555", "endLine": 64, "endColumn": 45, "suggestions": "577"}, {"ruleId": "529", "severity": 2, "message": "551", "line": 70, "column": 12, "nodeType": null, "messageId": "531", "endLine": 70, "endColumn": 17}, {"ruleId": "552", "severity": 2, "message": "553", "line": 89, "column": 42, "nodeType": "554", "messageId": "555", "endLine": 89, "endColumn": 45, "suggestions": "578"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 89, "column": 81, "nodeType": "554", "messageId": "555", "endLine": 89, "endColumn": 84, "suggestions": "579"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 89, "column": 118, "nodeType": "554", "messageId": "555", "endLine": 89, "endColumn": 121, "suggestions": "580"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 98, "column": 21, "nodeType": "554", "messageId": "555", "endLine": 98, "endColumn": 24, "suggestions": "581"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 99, "column": 29, "nodeType": "554", "messageId": "555", "endLine": 99, "endColumn": 32, "suggestions": "582"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 100, "column": 28, "nodeType": "554", "messageId": "555", "endLine": 100, "endColumn": 31, "suggestions": "583"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 101, "column": 27, "nodeType": "554", "messageId": "555", "endLine": 101, "endColumn": 30, "suggestions": "584"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 105, "column": 72, "nodeType": "554", "messageId": "555", "endLine": 105, "endColumn": 75, "suggestions": "585"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 152, "column": 60, "nodeType": "554", "messageId": "555", "endLine": 152, "endColumn": 63, "suggestions": "586"}, {"ruleId": "529", "severity": 2, "message": "551", "line": 44, "column": 14, "nodeType": null, "messageId": "531", "endLine": 44, "endColumn": 19}, {"ruleId": "529", "severity": 2, "message": "551", "line": 84, "column": 14, "nodeType": null, "messageId": "531", "endLine": 84, "endColumn": 19}, {"ruleId": "529", "severity": 2, "message": "551", "line": 111, "column": 14, "nodeType": null, "messageId": "531", "endLine": 111, "endColumn": 19}, {"ruleId": "547", "severity": 1, "message": "548", "line": 61, "column": 13, "nodeType": "549", "endLine": 65, "endColumn": 15}, {"ruleId": "547", "severity": 1, "message": "548", "line": 93, "column": 21, "nodeType": "549", "endLine": 97, "endColumn": 23}, {"ruleId": "529", "severity": 2, "message": "587", "line": 5, "column": 27, "nodeType": null, "messageId": "531", "endLine": 5, "endColumn": 34}, {"ruleId": "529", "severity": 2, "message": "588", "line": 5, "column": 36, "nodeType": null, "messageId": "531", "endLine": 5, "endColumn": 46}, {"ruleId": "536", "severity": 1, "message": "589", "line": 55, "column": 6, "nodeType": "538", "endLine": 55, "endColumn": 14, "suggestions": "590"}, {"ruleId": "547", "severity": 1, "message": "548", "line": 156, "column": 13, "nodeType": "549", "endLine": 160, "endColumn": 15}, {"ruleId": "552", "severity": 2, "message": "553", "line": 4, "column": 34, "nodeType": "554", "messageId": "555", "endLine": 4, "endColumn": 37, "suggestions": "591"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 65, "column": 60, "nodeType": "554", "messageId": "555", "endLine": 65, "endColumn": 63, "suggestions": "592"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 145, "column": 31, "nodeType": "554", "messageId": "555", "endLine": 145, "endColumn": 34, "suggestions": "593"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 151, "column": 26, "nodeType": "554", "messageId": "555", "endLine": 151, "endColumn": 29, "suggestions": "594"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 152, "column": 26, "nodeType": "554", "messageId": "555", "endLine": 152, "endColumn": 29, "suggestions": "595"}, {"ruleId": "529", "severity": 2, "message": "596", "line": 1, "column": 8, "nodeType": null, "messageId": "531", "endLine": 1, "endColumn": 16}, {"ruleId": "529", "severity": 2, "message": "597", "line": 27, "column": 13, "nodeType": null, "messageId": "531", "endLine": 27, "endColumn": 26}, {"ruleId": "529", "severity": 2, "message": "598", "line": 5, "column": 8, "nodeType": null, "messageId": "531", "endLine": 5, "endColumn": 12}, {"ruleId": "529", "severity": 2, "message": "599", "line": 89, "column": 11, "nodeType": null, "messageId": "531", "endLine": 89, "endColumn": 14}, {"ruleId": "552", "severity": 2, "message": "553", "line": 159, "column": 25, "nodeType": "554", "messageId": "555", "endLine": 159, "endColumn": 28, "suggestions": "600"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 38, "column": 18, "nodeType": "554", "messageId": "555", "endLine": 38, "endColumn": 21, "suggestions": "601"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 51, "column": 22, "nodeType": "554", "messageId": "555", "endLine": 51, "endColumn": 25, "suggestions": "602"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 88, "column": 52, "nodeType": "554", "messageId": "555", "endLine": 88, "endColumn": 55, "suggestions": "603"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 89, "column": 52, "nodeType": "554", "messageId": "555", "endLine": 89, "endColumn": 55, "suggestions": "604"}, {"ruleId": "529", "severity": 2, "message": "605", "line": 19, "column": 17, "nodeType": null, "messageId": "531", "endLine": 19, "endColumn": 24}, {"ruleId": "552", "severity": 2, "message": "553", "line": 20, "column": 38, "nodeType": "554", "messageId": "555", "endLine": 20, "endColumn": 41, "suggestions": "606"}, {"ruleId": "536", "severity": 1, "message": "607", "line": 36, "column": 6, "nodeType": "538", "endLine": 36, "endColumn": 23, "suggestions": "608"}, {"ruleId": "529", "severity": 2, "message": "540", "line": 62, "column": 14, "nodeType": null, "messageId": "531", "endLine": 62, "endColumn": 17}, {"ruleId": "529", "severity": 2, "message": "540", "line": 86, "column": 14, "nodeType": null, "messageId": "531", "endLine": 86, "endColumn": 17}, {"ruleId": "552", "severity": 2, "message": "553", "line": 16, "column": 36, "nodeType": "554", "messageId": "555", "endLine": 16, "endColumn": 39, "suggestions": "609"}, {"ruleId": "536", "severity": 1, "message": "610", "line": 32, "column": 6, "nodeType": "538", "endLine": 32, "endColumn": 22, "suggestions": "611"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 18, "column": 36, "nodeType": "554", "messageId": "555", "endLine": 18, "endColumn": 39, "suggestions": "612"}, {"ruleId": "536", "severity": 1, "message": "610", "line": 32, "column": 6, "nodeType": "538", "endLine": 32, "endColumn": 22, "suggestions": "613"}, {"ruleId": "529", "severity": 2, "message": "605", "line": 12, "column": 17, "nodeType": null, "messageId": "531", "endLine": 12, "endColumn": 24}, {"ruleId": "552", "severity": 2, "message": "553", "line": 13, "column": 36, "nodeType": "554", "messageId": "555", "endLine": 13, "endColumn": 39, "suggestions": "614"}, {"ruleId": "536", "severity": 1, "message": "610", "line": 28, "column": 6, "nodeType": "538", "endLine": 28, "endColumn": 22, "suggestions": "615"}, {"ruleId": "529", "severity": 2, "message": "551", "line": 81, "column": 14, "nodeType": null, "messageId": "531", "endLine": 81, "endColumn": 19}, {"ruleId": "529", "severity": 2, "message": "540", "line": 55, "column": 14, "nodeType": null, "messageId": "531", "endLine": 55, "endColumn": 17}, {"ruleId": "547", "severity": 1, "message": "548", "line": 491, "column": 19, "nodeType": "549", "endLine": 495, "endColumn": 21}, {"ruleId": "529", "severity": 2, "message": "559", "line": 5, "column": 27, "nodeType": null, "messageId": "531", "endLine": 5, "endColumn": 34}, {"ruleId": "552", "severity": 2, "message": "553", "line": 50, "column": 33, "nodeType": "554", "messageId": "555", "endLine": 50, "endColumn": 36, "suggestions": "616"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 63, "column": 33, "nodeType": "554", "messageId": "555", "endLine": 63, "endColumn": 36, "suggestions": "617"}, {"ruleId": "552", "severity": 2, "message": "553", "line": 185, "column": 22, "nodeType": "554", "messageId": "555", "endLine": 185, "endColumn": 25, "suggestions": "618"}, {"ruleId": "529", "severity": 2, "message": "619", "line": 25, "column": 16, "nodeType": null, "messageId": "531", "endLine": 25, "endColumn": 23}, {"ruleId": "536", "severity": 1, "message": "620", "line": 31, "column": 6, "nodeType": "538", "endLine": 31, "endColumn": 21, "suggestions": "621"}, {"ruleId": "529", "severity": 2, "message": "540", "line": 46, "column": 14, "nodeType": null, "messageId": "531", "endLine": 46, "endColumn": 17}, {"ruleId": "547", "severity": 1, "message": "548", "line": 61, "column": 19, "nodeType": "549", "endLine": 65, "endColumn": 21}, {"ruleId": "547", "severity": 1, "message": "548", "line": 195, "column": 27, "nodeType": "549", "endLine": 199, "endColumn": 29}, {"ruleId": "547", "severity": 1, "message": "548", "line": 180, "column": 7, "nodeType": "549", "endLine": 189, "endColumn": 9}, {"ruleId": "529", "severity": 2, "message": "622", "line": 37, "column": 10, "nodeType": null, "messageId": "531", "endLine": 37, "endColumn": 18}, {"ruleId": "623", "severity": 1, "message": "624", "line": 78, "column": 9, "nodeType": "549", "endLine": 82, "endColumn": 11}, {"ruleId": "623", "severity": 1, "message": "624", "line": 92, "column": 7, "nodeType": "549", "endLine": 96, "endColumn": 9}, {"ruleId": "552", "severity": 2, "message": "553", "line": 41, "column": 82, "nodeType": "554", "messageId": "555", "endLine": 41, "endColumn": 85, "suggestions": "625"}, "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["626"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["627"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'params' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["628", "629"], ["630", "631"], ["632", "633"], "'request' is defined but never used.", ["634", "635"], ["636", "637"], ["638", "639"], ["640", "641"], ["642", "643"], ["644", "645"], "React Hook useEffect has a missing dependency: 'fetchCategoryData'. Either include it or remove the dependency array.", ["646"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/tools/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["647"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'Eye' is defined but never used.", ["648", "649"], ["650", "651"], ["652", "653"], ["654", "655"], ["656", "657"], ["658", "659"], ["660", "661"], ["662", "663"], ["664", "665"], ["666", "667"], ["668", "669"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["670"], ["671", "672"], ["673", "674"], ["675", "676"], ["677", "678"], ["679", "680"], "'mongoose' is defined but never used.", "'paymentMethod' is assigned a value but never used.", "'Tool' is defined but never used.", "'now' is assigned a value but never used.", ["681", "682"], ["683", "684"], ["685", "686"], ["687", "688"], ["689", "690"], "'session' is assigned a value but never used.", ["691", "692"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["693"], ["694", "695"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["696"], ["697", "698"], ["699"], ["700", "701"], ["702"], ["703", "704"], ["705", "706"], ["707", "708"], "'setTool' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRelatedTools'. Either include it or remove the dependency array.", ["709"], "'hasError' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", ["710", "711"], {"desc": "712", "fix": "713"}, {"desc": "714", "fix": "715"}, {"messageId": "716", "fix": "717", "desc": "718"}, {"messageId": "719", "fix": "720", "desc": "721"}, {"messageId": "716", "fix": "722", "desc": "718"}, {"messageId": "719", "fix": "723", "desc": "721"}, {"messageId": "716", "fix": "724", "desc": "718"}, {"messageId": "719", "fix": "725", "desc": "721"}, {"messageId": "716", "fix": "726", "desc": "718"}, {"messageId": "719", "fix": "727", "desc": "721"}, {"messageId": "716", "fix": "728", "desc": "718"}, {"messageId": "719", "fix": "729", "desc": "721"}, {"messageId": "716", "fix": "730", "desc": "718"}, {"messageId": "719", "fix": "731", "desc": "721"}, {"messageId": "716", "fix": "732", "desc": "718"}, {"messageId": "719", "fix": "733", "desc": "721"}, {"messageId": "716", "fix": "734", "desc": "718"}, {"messageId": "719", "fix": "735", "desc": "721"}, {"messageId": "716", "fix": "736", "desc": "718"}, {"messageId": "719", "fix": "737", "desc": "721"}, {"desc": "738", "fix": "739"}, {"desc": "740", "fix": "741"}, {"messageId": "716", "fix": "742", "desc": "718"}, {"messageId": "719", "fix": "743", "desc": "721"}, {"messageId": "716", "fix": "744", "desc": "718"}, {"messageId": "719", "fix": "745", "desc": "721"}, {"messageId": "716", "fix": "746", "desc": "718"}, {"messageId": "719", "fix": "747", "desc": "721"}, {"messageId": "716", "fix": "748", "desc": "718"}, {"messageId": "719", "fix": "749", "desc": "721"}, {"messageId": "716", "fix": "750", "desc": "718"}, {"messageId": "719", "fix": "751", "desc": "721"}, {"messageId": "716", "fix": "752", "desc": "718"}, {"messageId": "719", "fix": "753", "desc": "721"}, {"messageId": "716", "fix": "754", "desc": "718"}, {"messageId": "719", "fix": "755", "desc": "721"}, {"messageId": "716", "fix": "756", "desc": "718"}, {"messageId": "719", "fix": "757", "desc": "721"}, {"messageId": "716", "fix": "758", "desc": "718"}, {"messageId": "719", "fix": "759", "desc": "721"}, {"messageId": "716", "fix": "760", "desc": "718"}, {"messageId": "719", "fix": "761", "desc": "721"}, {"messageId": "716", "fix": "762", "desc": "718"}, {"messageId": "719", "fix": "763", "desc": "721"}, {"desc": "764", "fix": "765"}, {"messageId": "716", "fix": "766", "desc": "718"}, {"messageId": "719", "fix": "767", "desc": "721"}, {"messageId": "716", "fix": "768", "desc": "718"}, {"messageId": "719", "fix": "769", "desc": "721"}, {"messageId": "716", "fix": "770", "desc": "718"}, {"messageId": "719", "fix": "771", "desc": "721"}, {"messageId": "716", "fix": "772", "desc": "718"}, {"messageId": "719", "fix": "773", "desc": "721"}, {"messageId": "716", "fix": "774", "desc": "718"}, {"messageId": "719", "fix": "775", "desc": "721"}, {"messageId": "716", "fix": "776", "desc": "718"}, {"messageId": "719", "fix": "777", "desc": "721"}, {"messageId": "716", "fix": "778", "desc": "718"}, {"messageId": "719", "fix": "779", "desc": "721"}, {"messageId": "716", "fix": "780", "desc": "718"}, {"messageId": "719", "fix": "781", "desc": "721"}, {"messageId": "716", "fix": "782", "desc": "718"}, {"messageId": "719", "fix": "783", "desc": "721"}, {"messageId": "716", "fix": "784", "desc": "718"}, {"messageId": "719", "fix": "785", "desc": "721"}, {"messageId": "716", "fix": "786", "desc": "718"}, {"messageId": "719", "fix": "787", "desc": "721"}, {"desc": "788", "fix": "789"}, {"messageId": "716", "fix": "790", "desc": "718"}, {"messageId": "719", "fix": "791", "desc": "721"}, {"desc": "792", "fix": "793"}, {"messageId": "716", "fix": "794", "desc": "718"}, {"messageId": "719", "fix": "795", "desc": "721"}, {"desc": "792", "fix": "796"}, {"messageId": "716", "fix": "797", "desc": "718"}, {"messageId": "719", "fix": "798", "desc": "721"}, {"desc": "792", "fix": "799"}, {"messageId": "716", "fix": "800", "desc": "718"}, {"messageId": "719", "fix": "801", "desc": "721"}, {"messageId": "716", "fix": "802", "desc": "718"}, {"messageId": "719", "fix": "803", "desc": "721"}, {"messageId": "716", "fix": "804", "desc": "718"}, {"messageId": "719", "fix": "805", "desc": "721"}, {"desc": "806", "fix": "807"}, {"messageId": "716", "fix": "808", "desc": "718"}, {"messageId": "719", "fix": "809", "desc": "721"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "810", "text": "811"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "812", "text": "813"}, "suggestUnknown", {"range": "814", "text": "815"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "816", "text": "817"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "818", "text": "815"}, {"range": "819", "text": "817"}, {"range": "820", "text": "815"}, {"range": "821", "text": "817"}, {"range": "822", "text": "815"}, {"range": "823", "text": "817"}, {"range": "824", "text": "815"}, {"range": "825", "text": "817"}, {"range": "826", "text": "815"}, {"range": "827", "text": "817"}, {"range": "828", "text": "815"}, {"range": "829", "text": "817"}, {"range": "830", "text": "815"}, {"range": "831", "text": "817"}, {"range": "832", "text": "815"}, {"range": "833", "text": "817"}, "Update the dependencies array to be: [fetchCategoryData, params.slug]", {"range": "834", "text": "835"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "836", "text": "837"}, {"range": "838", "text": "815"}, {"range": "839", "text": "817"}, {"range": "840", "text": "815"}, {"range": "841", "text": "817"}, {"range": "842", "text": "815"}, {"range": "843", "text": "817"}, {"range": "844", "text": "815"}, {"range": "845", "text": "817"}, {"range": "846", "text": "815"}, {"range": "847", "text": "817"}, {"range": "848", "text": "815"}, {"range": "849", "text": "817"}, {"range": "850", "text": "815"}, {"range": "851", "text": "817"}, {"range": "852", "text": "815"}, {"range": "853", "text": "817"}, {"range": "854", "text": "815"}, {"range": "855", "text": "817"}, {"range": "856", "text": "815"}, {"range": "857", "text": "817"}, {"range": "858", "text": "815"}, {"range": "859", "text": "817"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "860", "text": "861"}, {"range": "862", "text": "815"}, {"range": "863", "text": "817"}, {"range": "864", "text": "815"}, {"range": "865", "text": "817"}, {"range": "866", "text": "815"}, {"range": "867", "text": "817"}, {"range": "868", "text": "815"}, {"range": "869", "text": "817"}, {"range": "870", "text": "815"}, {"range": "871", "text": "817"}, {"range": "872", "text": "815"}, {"range": "873", "text": "817"}, {"range": "874", "text": "815"}, {"range": "875", "text": "817"}, {"range": "876", "text": "815"}, {"range": "877", "text": "817"}, {"range": "878", "text": "815"}, {"range": "879", "text": "817"}, {"range": "880", "text": "815"}, {"range": "881", "text": "817"}, {"range": "882", "text": "815"}, {"range": "883", "text": "817"}, "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "884", "text": "885"}, {"range": "886", "text": "815"}, {"range": "887", "text": "817"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "888", "text": "889"}, {"range": "890", "text": "815"}, {"range": "891", "text": "817"}, {"range": "892", "text": "889"}, {"range": "893", "text": "815"}, {"range": "894", "text": "817"}, {"range": "895", "text": "889"}, {"range": "896", "text": "815"}, {"range": "897", "text": "817"}, {"range": "898", "text": "815"}, {"range": "899", "text": "817"}, {"range": "900", "text": "815"}, {"range": "901", "text": "817"}, "Update the dependencies array to be: [fetchRelatedTools, tool.category]", {"range": "902", "text": "903"}, {"range": "904", "text": "815"}, {"range": "905", "text": "817"}, [1125, 1136], "[fetchStats, timeRange]", [1646, 1660], "[fetchTools, statusFilter]", [3022, 3025], "unknown", [3022, 3025], "never", [672, 675], [672, 675], [1551, 1554], [1551, 1554], [2591, 2594], [2591, 2594], [4541, 4544], [4541, 4544], [5532, 5535], [5532, 5535], [802, 805], [802, 805], [1909, 1912], [1909, 1912], [4431, 4434], [4431, 4434], [1651, 1664], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, params.slug]", [1532, 1575], "[filterTools, likedTools, searchQuery, selectedCategory]", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [2602, 2605], [2602, 2605], [2641, 2644], [2641, 2644], [2678, 2681], [2678, 2681], [2852, 2855], [2852, 2855], [2901, 2904], [2901, 2904], [2960, 2963], [2960, 2963], [3018, 3021], [3018, 3021], [3143, 3146], [3143, 3146], [4793, 4796], [4793, 4796], [1439, 1447], "[fetchComments, toolId]", [137, 140], [137, 140], [2074, 2077], [2074, 2077], [4406, 4409], [4406, 4409], [4562, 4565], [4562, 4565], [4621, 4624], [4621, 4624], [4266, 4269], [4266, 4269], [1171, 1174], [1171, 1174], [1442, 1445], [1442, 1445], [2429, 2432], [2429, 2432], [2517, 2520], [2517, 2520], [809, 812], [809, 812], [1213, 1230], "[status, orderId, router, fetchOrderInfo]", [617, 620], [617, 620], [910, 926], "[fetchToolInfo, router, status, toolId]", [604, 607], [604, 607], [895, 911], [511, 514], [511, 514], [859, 875], [1561, 1564], [1561, 1564], [2024, 2027], [2024, 2027], [4765, 4768], [4765, 4768], [908, 923], "[fetchRelatedTools, tool.category]", [1084, 1087], [1084, 1087]]