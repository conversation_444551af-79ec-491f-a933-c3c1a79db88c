module.exports = {

"[project]/.next-internal/server/app/api/tools/[id]/like/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function dbConnect() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = dbConnect;
}}),
"[project]/src/models/User.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const AccountSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    provider: {
        type: String,
        required: true,
        enum: [
            'google',
            'github',
            'email'
        ]
    },
    providerId: {
        type: String,
        required: true
    },
    providerAccountId: {
        type: String,
        required: true
    },
    accessToken: String,
    refreshToken: String,
    expiresAt: Date
}, {
    _id: false
});
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    email: {
        type: String,
        required: [
            true,
            'Email is required'
        ],
        unique: true,
        trim: true,
        lowercase: true,
        validate: {
            validator: function(v) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
            },
            message: 'Please enter a valid email address'
        }
    },
    name: {
        type: String,
        required: [
            true,
            'Name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Name cannot exceed 100 characters'
        ]
    },
    avatar: {
        type: String,
        trim: true
    },
    role: {
        type: String,
        required: true,
        enum: [
            'user',
            'admin'
        ],
        default: 'user'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    // 认证相关
    emailVerified: {
        type: Boolean,
        default: false
    },
    emailVerificationToken: {
        type: String,
        trim: true
    },
    emailVerificationExpires: {
        type: Date
    },
    // OAuth账户关联
    accounts: [
        AccountSchema
    ],
    // 用户行为
    submittedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    likedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    comments: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Comment'
        }
    ],
    // 时间戳
    lastLoginAt: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
UserSchema.index({
    emailVerificationToken: 1
});
UserSchema.index({
    'accounts.provider': 1,
    'accounts.providerAccountId': 1
});
// 实例方法
UserSchema.methods.addAccount = function(account) {
    // 检查是否已存在相同的账户
    const existingAccount = this.accounts.find((acc)=>acc.provider === account.provider && acc.providerAccountId === account.providerAccountId);
    if (!existingAccount) {
        this.accounts.push(account);
    } else {
        // 更新现有账户信息
        Object.assign(existingAccount, account);
    }
};
UserSchema.methods.removeAccount = function(provider, providerAccountId) {
    this.accounts = this.accounts.filter((acc)=>!(acc.provider === provider && acc.providerAccountId === providerAccountId));
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/google.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/github.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
;
;
;
;
;
const authOptions = {
    // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略
    // adapter: MongoDBAdapter(client),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GITHUB_CLIENT_ID,
            clientSecret: process.env.GITHUB_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            id: 'email-code',
            name: 'Email Code',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                code: {
                    label: 'Code',
                    type: 'text'
                },
                token: {
                    label: 'Token',
                    type: 'text'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.code || !credentials?.token) {
                    return null;
                }
                try {
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
                    // 查找用户
                    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                        email: credentials.email.toLowerCase(),
                        emailVerificationExpires: {
                            $gt: new Date()
                        }
                    });
                    if (!user) {
                        return null;
                    }
                    // 验证token和验证码
                    const storedData = user.emailVerificationToken;
                    if (!storedData || !storedData.includes(':')) {
                        return null;
                    }
                    const [storedToken, storedCode] = storedData.split(':');
                    if (storedToken !== credentials.token || storedCode !== credentials.code) {
                        return null;
                    }
                    // 验证成功，更新用户状态
                    user.emailVerified = true;
                    user.emailVerificationToken = undefined;
                    user.emailVerificationExpires = undefined;
                    user.lastLoginAt = new Date();
                    // 如果用户没有邮箱账户记录，添加一个
                    const hasEmailAccount = user.accounts.some((acc)=>acc.provider === 'email');
                    if (!hasEmailAccount) {
                        user.accounts.push({
                            provider: 'email',
                            providerId: 'email',
                            providerAccountId: user.email
                        });
                    }
                    await user.save();
                    return {
                        id: user._id.toString(),
                        email: user.email,
                        name: user.name,
                        image: user.avatar,
                        role: user.role
                    };
                } catch (error) {
                    console.error('Email code authorization error:', error);
                    return null;
                }
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async signIn ({ user, account, profile }) {
            // 对于credentials provider，用户已经在authorize中处理过了
            if (account?.provider === 'email-code') {
                return true;
            }
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            try {
                // 查找或创建用户（仅用于OAuth providers）
                let existingUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                    email: user.email
                });
                if (!existingUser) {
                    // 创建新用户
                    existingUser = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                        email: user.email,
                        name: user.name || profile?.name || 'User',
                        avatar: user.image || profile?.image,
                        emailVerified: true,
                        lastLoginAt: new Date()
                    });
                    await existingUser.save();
                } else {
                    // 更新最后登录时间
                    existingUser.lastLoginAt = new Date();
                    await existingUser.save();
                }
                // 添加或更新账户信息
                if (account && account.provider !== 'email-code') {
                    existingUser.addAccount({
                        provider: account.provider,
                        providerId: account.provider,
                        providerAccountId: account.providerAccountId || account.id || '',
                        accessToken: account.access_token,
                        refreshToken: account.refresh_token,
                        expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined
                    });
                    await existingUser.save();
                }
                return true;
            } catch (error) {
                console.error('Sign in error:', error);
                return false;
            }
        },
        async jwt ({ token, user }) {
            if (user) {
                // 对于credentials provider，user对象已经包含了我们需要的信息
                token.userId = user.id;
                token.role = user.role || 'user';
            }
            return token;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                session.user.id = token.userId;
                session.user.role = token.role;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        error: '/auth/error'
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/constants/categories.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// AI工具分类的统一配置文件
// 这个文件包含所有分类的完整信息，确保整个应用中的一致性
__turbopack_context__.s({
    "CATEGORY_CONFIGS": (()=>CATEGORY_CONFIGS),
    "CATEGORY_LABELS": (()=>CATEGORY_LABELS),
    "CATEGORY_METADATA": (()=>CATEGORY_METADATA),
    "CATEGORY_OPTIONS": (()=>CATEGORY_OPTIONS),
    "CATEGORY_OPTIONS_WITH_ALL": (()=>CATEGORY_OPTIONS_WITH_ALL),
    "CATEGORY_SLUGS": (()=>CATEGORY_SLUGS),
    "getCategoryConfig": (()=>getCategoryConfig),
    "getCategoryName": (()=>getCategoryName),
    "isValidCategory": (()=>isValidCategory)
});
const CATEGORY_CONFIGS = [
    {
        slug: 'text-generation',
        name: '文本生成',
        description: '利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等',
        icon: '📝',
        color: '#3B82F6'
    },
    {
        slug: 'image-generation',
        name: '图像生成',
        description: '使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等',
        icon: '🎨',
        color: '#10B981'
    },
    {
        slug: 'code-generation',
        name: '代码生成',
        description: '智能代码生成和编程辅助工具，提高开发效率',
        icon: '💻',
        color: '#8B5CF6'
    },
    {
        slug: 'data-analysis',
        name: '数据分析',
        description: '数据分析和可视化工具，帮助洞察数据价值',
        icon: '📊',
        color: '#F59E0B'
    },
    {
        slug: 'audio-processing',
        name: '音频处理',
        description: '音频处理、语音合成、音乐生成等音频AI工具',
        icon: '🎵',
        color: '#EF4444'
    },
    {
        slug: 'video-editing',
        name: '视频编辑',
        description: '视频生成、编辑、剪辑等视频处理AI工具',
        icon: '🎬',
        color: '#06B6D4'
    },
    {
        slug: 'translation',
        name: '语言翻译',
        description: '多语言翻译和本地化AI工具',
        icon: '🌐',
        color: '#84CC16'
    },
    {
        slug: 'search-engines',
        name: '搜索引擎',
        description: '智能搜索和信息检索AI工具',
        icon: '🔍',
        color: '#F97316'
    },
    {
        slug: 'education',
        name: '教育学习',
        description: '教育培训和学习辅助AI工具',
        icon: '📚',
        color: '#A855F7'
    },
    {
        slug: 'marketing',
        name: '营销工具',
        description: '数字营销和推广AI工具',
        icon: '📈',
        color: '#EC4899'
    },
    {
        slug: 'productivity',
        name: '生产力工具',
        description: '提高工作效率的AI工具',
        icon: '⚡',
        color: '#14B8A6'
    },
    {
        slug: 'customer-service',
        name: '客户服务',
        description: '客户支持和服务AI工具',
        icon: '🎧',
        color: '#F59E0B'
    }
];
const CATEGORY_OPTIONS = CATEGORY_CONFIGS.map((config)=>({
        value: config.slug,
        label: config.name
    }));
const CATEGORY_OPTIONS_WITH_ALL = [
    {
        value: '',
        label: '所有分类'
    },
    ...CATEGORY_OPTIONS
];
const CATEGORY_LABELS = CATEGORY_CONFIGS.reduce((acc, config)=>{
    acc[config.slug] = config.name;
    return acc;
}, {});
const CATEGORY_METADATA = CATEGORY_CONFIGS.reduce((acc, config)=>{
    acc[config.slug] = config;
    return acc;
}, {});
const getCategoryConfig = (slug)=>{
    return CATEGORY_METADATA[slug];
};
const getCategoryName = (slug)=>{
    return CATEGORY_LABELS[slug] || slug;
};
const isValidCategory = (slug)=>{
    return slug in CATEGORY_METADATA;
};
const CATEGORY_SLUGS = CATEGORY_CONFIGS.map((config)=>config.slug);
}}),
"[project]/src/models/Tool.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/categories.ts [app-route] (ecmascript)");
;
;
const ToolSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: [
            true,
            'Tool name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Tool name cannot exceed 100 characters'
        ]
    },
    tagline: {
        type: String,
        trim: true,
        maxlength: [
            200,
            'Tagline cannot exceed 200 characters'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Tool description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot exceed 500 characters'
        ]
    },
    longDescription: {
        type: String,
        trim: true,
        maxlength: [
            2000,
            'Long description cannot exceed 2000 characters'
        ]
    },
    website: {
        type: String,
        required: [
            true,
            'Website URL is required'
        ],
        trim: true,
        validate: {
            validator: function(v) {
                return /^https?:\/\/.+/.test(v);
            },
            message: 'Please enter a valid URL'
        }
    },
    logo: {
        type: String,
        trim: true
    },
    category: {
        type: String,
        required: [
            true,
            'Category is required'
        ],
        enum: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CATEGORY_SLUGS"]
    },
    tags: [
        {
            type: String,
            trim: true,
            lowercase: true
        }
    ],
    pricing: {
        type: String,
        required: [
            true,
            'Pricing model is required'
        ],
        enum: [
            'free',
            'freemium',
            'paid'
        ]
    },
    pricingDetails: {
        type: String,
        trim: true,
        maxlength: [
            500,
            'Pricing details cannot exceed 500 characters'
        ]
    },
    screenshots: [
        {
            type: String,
            trim: true
        }
    ],
    submittedBy: {
        type: String,
        required: [
            true,
            'Submitter ID is required'
        ],
        trim: true
    },
    submittedAt: {
        type: Date,
        default: Date.now
    },
    launchDate: {
        type: Date
    },
    status: {
        type: String,
        required: true,
        enum: [
            'draft',
            'pending',
            'approved',
            'rejected'
        ],
        default: 'draft'
    },
    reviewNotes: {
        type: String,
        trim: true,
        maxlength: [
            1000,
            'Review notes cannot exceed 1000 characters'
        ]
    },
    reviewedBy: {
        type: String,
        trim: true
    },
    reviewedAt: {
        type: Date
    },
    // 发布日期选择相关
    launchDateSelected: {
        type: Boolean,
        default: false
    },
    selectedLaunchDate: {
        type: Date
    },
    launchOption: {
        type: String,
        enum: [
            'free',
            'paid'
        ]
    },
    // 付费相关
    paymentRequired: {
        type: Boolean,
        default: false
    },
    paymentAmount: {
        type: Number,
        min: 0
    },
    paymentStatus: {
        type: String,
        enum: [
            'pending',
            'completed',
            'failed',
            'refunded'
        ]
    },
    orderId: {
        type: String,
        trim: true
    },
    paymentMethod: {
        type: String,
        trim: true
    },
    paidAt: {
        type: Date
    },
    views: {
        type: Number,
        default: 0,
        min: 0
    },
    likes: {
        type: Number,
        default: 0,
        min: 0
    },
    likedBy: [
        {
            type: String,
            trim: true
        }
    ],
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes for better query performance
ToolSchema.index({
    status: 1,
    isActive: 1
});
ToolSchema.index({
    category: 1,
    status: 1
});
ToolSchema.index({
    tags: 1,
    status: 1
});
ToolSchema.index({
    submittedBy: 1
});
ToolSchema.index({
    launchDate: -1
});
ToolSchema.index({
    views: -1
});
ToolSchema.index({
    likes: -1
});
// Text search index
ToolSchema.index({
    name: 'text',
    tagline: 'text',
    description: 'text',
    longDescription: 'text',
    tags: 'text'
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Tool || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Tool', ToolSchema);
}}),
"[project]/src/app/api/tools/[id]/like/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Tool.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
;
;
;
;
;
;
async function POST(request, { params }) {
    try {
        // 检查用户认证
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user?.email) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '请先登录'
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // 获取用户信息
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: session.user.email
        });
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '用户不存在'
            }, {
                status: 404
            });
        }
        const { id: toolId } = await params;
        // 查找工具
        const tool = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(toolId);
        if (!tool) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '工具不存在'
            }, {
                status: 404
            });
        }
        // 检查请求体中是否有强制操作标识
        const body = await request.json().catch(()=>({}));
        const forceUnlike = body.forceUnlike === true;
        console.log('force unlike..........', forceUnlike, toolId, user._id, tool.likedBy, user.likedTools);
        if (forceUnlike) {
            // 强制取消点赞 - 直接从两个地方移除，不管当前状态
            tool.likedBy = tool.likedBy.filter((id)=>id?.toString() !== user._id.toString());
            user.likedTools = user.likedTools.filter((id)=>id?.toString() !== toolId);
            // 重新计算likes数量以确保准确性
            tool.likes = tool.likedBy.length;
            // 保存更改
            await tool.save();
            await user.save();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    liked: false,
                    likes: tool.likes
                }
            });
        }
        // 原有的切换逻辑 - 检查用户是否已经点赞
        const hasLikedInTool = tool.likedBy.includes(user._id.toString());
        const hasLikedInUser = user.likedTools.includes(toolId);
        // 处理数据不一致的情况
        if (hasLikedInTool !== hasLikedInUser) {
            console.warn(`Data inconsistency detected for user ${user._id} and tool ${toolId}:`, {
                hasLikedInTool,
                hasLikedInUser,
                toolLikedBy: tool.likedBy,
                userLikedTools: user.likedTools
            });
        }
        // 使用逻辑OR来决定当前状态 - 如果任一数据源显示已点赞，则认为已点赞
        const currentlyLiked = hasLikedInTool || hasLikedInUser;
        if (currentlyLiked) {
            // 取消点赞 - 确保从两个地方都移除
            tool.likedBy = tool.likedBy.filter((id)=>id !== user._id.toString());
            user.likedTools = user.likedTools.filter((id)=>id !== toolId);
            // 重新计算likes数量以确保准确性
            tool.likes = tool.likedBy.length;
        } else {
            // 添加点赞 - 确保添加到两个地方且避免重复
            if (!tool.likedBy.includes(user._id.toString())) {
                tool.likedBy.push(user._id.toString());
            }
            if (!user.likedTools.includes(toolId)) {
                user.likedTools.push(toolId);
            }
            // 重新计算likes数量以确保准确性
            tool.likes = tool.likedBy.length;
        }
        // 保存更改
        await tool.save();
        await user.save();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                liked: !currentlyLiked,
                likes: tool.likes
            }
        });
    } catch (error) {
        console.error('Like tool error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: '服务器错误'
        }, {
            status: 500
        });
    }
}
async function GET(_request, { params }) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { id: toolId } = await params;
        const tool = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(toolId);
        if (!tool) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '工具不存在'
            }, {
                status: 404
            });
        }
        let liked = false;
        if (session?.user?.email) {
            const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                email: session.user.email
            });
            if (user) {
                // 检查两个数据源的一致性
                const hasLikedInTool = tool.likedBy.includes(user._id.toString());
                const hasLikedInUser = user.likedTools.includes(toolId);
                // 如果数据不一致，记录警告
                if (hasLikedInTool !== hasLikedInUser) {
                    console.warn(`Data inconsistency detected in GET for user ${user._id} and tool ${toolId}:`, {
                        hasLikedInTool,
                        hasLikedInUser
                    });
                }
                // 使用逻辑OR来决定状态
                liked = hasLikedInTool || hasLikedInUser;
            }
        }
        // 确保likes数量与likedBy数组长度一致
        const actualLikes = tool.likedBy.length;
        if (tool.likes !== actualLikes) {
            console.warn(`Likes count inconsistency for tool ${toolId}: stored=${tool.likes}, actual=${actualLikes}`);
            // 可以选择在这里修复数据
            tool.likes = actualLikes;
            await tool.save();
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                liked,
                likes: tool.likes
            }
        });
    } catch (error) {
        console.error('Get like status error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: '服务器错误'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a7397e7d._.js.map