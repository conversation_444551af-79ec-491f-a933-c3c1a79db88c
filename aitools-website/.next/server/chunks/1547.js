"use strict";exports.id=1547,exports.ids=[1547],exports.modules={5973:(e,t,r)=>{function s(){return null}r.d(t,{default:()=>s}),r(43210)},11011:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(60687),a=r(93613),l=r(11860);function i({message:e,onClose:t,className:r=""}){return(0,s.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${r}`,children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:e})}),t&&(0,s.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,s.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},25541:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},33823:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(60687);function a({size:e="md",className:t=""}){return(0,s.jsx)("div",{className:`flex justify-center items-center ${t}`,children:(0,s.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},62185:(e,t,r)=>{r.d(t,{u:()=>l});let s=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{constructor(e=s){this.baseURL=e}async request(e,t={}){try{let r=`${this.baseURL}${e}`,s={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(r,s),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/tools${r?`?${r}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/user/liked-tools${r?`?${r}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/admin/tools${r?`?${r}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}}let l=new a},62688:(e,t,r)=>{r.d(t,{A:()=>h});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:d,...h},m)=>(0,s.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",l),...!i&&!o(h)&&{"aria-hidden":"true"},...h},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),h=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...l},o)=>(0,s.createElement)(d,{ref:o,iconNode:t,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,r),...l}));return r.displayName=i(e),r}},70440:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},93613:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},98402:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(60687);r(43210);var a=r(85814),l=r.n(a),i=r(5973);let n=({children:e})=>(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(i.default,{}),(0,s.jsx)("main",{className:"flex-1",children:e}),(0,s.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,s.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,s.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}};