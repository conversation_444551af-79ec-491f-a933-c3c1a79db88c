(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10379:(e,s,t)=>{Promise.resolve().then(t.bind(t,12454))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(93613),l=t(11860);function i({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12454:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(60687),a=t(43210),l=t(98402),i=t(33823),n=t(11011),o=t(78890),d=t(62185),c=t(48730),x=t(5336),m=t(35071),u=t(99891),h=t(99270),p=t(80462),g=t(43649),y=t(58869),f=t(40228),j=t(25334),b=t(13861);let v={"text-generation":"文本生成","image-generation":"图像生成","code-generation":"代码生成","data-analysis":"数据分析","audio-processing":"音频处理","video-editing":"视频编辑",translation:"语言翻译","search-engines":"搜索引擎",education:"教育学习",marketing:"营销工具",productivity:"生产力工具","customer-service":"客户服务"},N={free:"免费",freemium:"免费增值",paid:"付费"};function w(){let[e,s]=(0,a.useState)([]),[t,w]=(0,a.useState)(!0),[A,k]=(0,a.useState)(""),[S,C]=(0,a.useState)(""),[P,q]=(0,a.useState)(""),[_,T]=(0,a.useState)("all"),[$,L]=(0,a.useState)(null),[E,U]=(0,a.useState)(!1),[O,R]=(0,a.useState)(""),[z,D]=(0,a.useState)(!1),M=async()=>{try{w(!0),k("");let e=await d.u.getAdminTools({status:"all"===_?void 0:_,limit:50});e.success&&e.data?s(e.data.tools):k(e.error||"获取工具列表失败")}catch(e){k("网络错误，请重试")}finally{w(!1)}},G=e.filter(e=>{let s=e.name.toLowerCase().includes(P.toLowerCase())||e.description.toLowerCase().includes(P.toLowerCase()),t="all"===_||e.status===_;return s&&t}),I=async e=>{try{D(!0),k("");let s=await d.u.approveTool(e,{reviewedBy:"admin",reviewNotes:"审核通过",launchDate:new Date().toISOString()});s.success?(C("工具审核通过！"),await M()):k(s.error||"审核操作失败")}catch(e){k("网络错误，请重试")}finally{D(!1)}},B=async(e,s)=>{try{D(!0),k("");let t=await d.u.rejectTool(e,{reviewedBy:"admin",rejectReason:s});t.success?(C("工具已拒绝！"),await M(),U(!1),R(""),L(null)):k(t.error||"拒绝操作失败")}catch(e){k("网络错误，请重试")}finally{D(!1)}},J=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),H=e=>{switch(e){case"pending":return(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(c.A,{className:"w-3 h-3 mr-1"}),"待审核"]});case"approved":return(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,r.jsx)(x.A,{className:"w-3 h-3 mr-1"}),"已批准"]});case"rejected":return(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,r.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"已拒绝"]});default:return null}};return t?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(i.A,{size:"lg",className:"py-20"})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,r.jsx)(u.A,{className:"mr-3 h-8 w-8 text-blue-600"}),"管理员审核中心"]}),(0,r.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"审核和管理用户提交的 AI 工具"})]}),(0,r.jsxs)("div",{className:"flex space-x-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:e.filter(e=>"pending"===e.status).length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"待审核"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.filter(e=>"approved"===e.status).length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"已批准"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:e.filter(e=>"rejected"===e.status).length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"已拒绝"})]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或提交者...",value:P,onChange:e=>q(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,r.jsx)("div",{className:"sm:w-48",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"}),(0,r.jsxs)("select",{value:_,onChange:e=>T(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none",children:[(0,r.jsx)("option",{value:"all",children:"所有状态"}),(0,r.jsx)("option",{value:"pending",children:"待审核"}),(0,r.jsx)("option",{value:"approved",children:"已批准"}),(0,r.jsx)("option",{value:"rejected",children:"已拒绝"})]})]})})]})}),S&&(0,r.jsx)(o.A,{message:S,onClose:()=>C(""),className:"mb-6"}),A&&(0,r.jsx)(n.A,{message:A,onClose:()=>k(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===G.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(g.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"没有找到工具"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:P||"all"!==_?"尝试调整搜索条件或筛选器":"暂无待审核的工具"})]}):(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:G.map(e=>(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("img",{src:e.logo,alt:e.name,className:"w-12 h-12 rounded-lg object-cover border border-gray-200"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 truncate",children:e.name}),H(e.status),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:v[e.category]}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:N[e.pricing]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 mr-1"}),e.submittedBy]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 mr-1"}),J(e.submittedAt)]}),e.launchDate&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-1"}),"已发布: ",new Date(e.launchDate).toLocaleDateString("zh-CN")]})]}),e.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-3",children:e.tags.map((e,s)=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700",children:e},s))})]})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"访问网站",children:(0,r.jsx)(j.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"查看详情",children:(0,r.jsx)(b.A,{className:"w-4 h-4"})}),"pending"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>I(e._id),disabled:z,className:"px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:z?"处理中...":"批准"}),(0,r.jsx)("button",{onClick:()=>{L(e._id),U(!0)},disabled:z,className:"px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"拒绝"})]})]})]})},e._id))})}),E&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"拒绝工具"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请说明拒绝的原因，这将帮助提交者改进他们的提交。"}),(0,r.jsx)("textarea",{value:O,onChange:e=>R(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入拒绝原因..."}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>{U(!1),R(""),L(null)},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors",children:"取消"}),(0,r.jsx)("button",{onClick:()=>$&&B($,O),disabled:!O.trim()||z,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:z?"处理中...":"确认拒绝"})]})]})})]})})}},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15955:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},16794:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({size:e="md",className:s=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${s}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},62185:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let r=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{constructor(e=r){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}}let l=new a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78890:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(5336),l=t(11860);function i({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,6707,9529],()=>t(16794));module.exports=r})();