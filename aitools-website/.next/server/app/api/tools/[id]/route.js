(()=>{var e={};e.id=7227,e.ids=[7227],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>c});var s=r(36344),i=r(65752),n=r(13581),a=r(75745),o=r(17063);let c={providers:[(0,s.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,n.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,a.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[s,i]=r.split(":");if(s!==e.token||i!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,a.A)();try{let s=await o.A.findOne({email:e.email});return s?s.lastLoginAt=new Date:s=new o.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await s.save(),t&&"email-code"!==t.provider&&(s.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await s.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),i=r.n(s);let n=new s.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),a=new s.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[n],submittedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:s.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({email:1}),a.index({role:1}),a.index({emailVerificationToken:1}),a.index({"accounts.provider":1,"accounts.providerAccountId":1}),a.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},a.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let o=i().models.User||i().model("User",a)},18508:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>A,routeModule:()=>x,serverHooks:()=>S,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{DELETE:()=>f,GET:()=>y,PUT:()=>v});var i=r(96559),n=r(48088),a=r(37719),o=r(32190),c=r(35426),u=r(75745),d=r(30762),l=r(17063),p=r(12909),m=r(56037),g=r.n(m);async function y(e,{params:t}){try{let e;await (0,u.A)();let{id:r}=await t;if(!g().Types.ObjectId.isValid(r))return o.NextResponse.json({success:!1,message:"无效的工具ID"},{status:400});let s=await d.A.findById(r);if(!s)return o.NextResponse.json({success:!1,message:"工具不存在"},{status:404});if("draft"===s.status){let e=await (0,c.getServerSession)(p.N);if(!e?.user?.email)return o.NextResponse.json({success:!1,message:"请先登录"},{status:401});let t=await l.A.findOne({email:e.user.email});if(!t||s.submittedBy!==t._id.toString())return o.NextResponse.json({success:!1,message:"您没有权限访问此工具"},{status:403})}return"approved"===s.status&&s.launchDate&&new Date(s.launchDate)<=new Date&&s.isActive&&await d.A.findByIdAndUpdate(r,{$inc:{views:1}}),e="draft"===s.status?s:{...s.toObject(),submittedBy:void 0,reviewNotes:void 0,reviewedBy:void 0},o.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching tool:",e),o.NextResponse.json({success:!1,message:"获取工具详情失败"},{status:500})}}async function v(e,{params:t}){try{let r=await (0,c.getServerSession)(p.N);if(!r?.user?.email)return o.NextResponse.json({success:!1,error:"请先登录"},{status:401});await (0,u.A)();let{id:s}=await t,i=await e.json();if(!g().Types.ObjectId.isValid(s))return o.NextResponse.json({success:!1,error:"无效的工具ID"},{status:400});let n=await l.A.findOne({email:r.user.email});if(!n)return o.NextResponse.json({success:!1,error:"用户不存在"},{status:404});let a=await d.A.findById(s);if(!a)return o.NextResponse.json({success:!1,error:"工具不存在"},{status:404});if(a.submittedBy!==n._id.toString())return o.NextResponse.json({success:!1,error:"您没有权限编辑此工具"},{status:403});if(!["draft","pending","rejected","approved","published"].includes(a.status))return o.NextResponse.json({success:!1,error:"该工具当前状态不允许编辑"},{status:400});let m=[];["draft","pending","rejected"].includes(a.status)?m=["name","tagline","description","website","logo","category","pricing","tags"]:"approved"===a.status&&(m=a.launchDate&&new Date(a.launchDate)<=new Date?["name","tagline","description"]:["name","tagline","description","logo"]);let y={};for(let e of m)void 0!==i[e]&&(y[e]=i[e]);if(y.name&&y.name!==a.name&&await d.A.findOne({name:y.name,_id:{$ne:s}}))return o.NextResponse.json({success:!1,error:"该工具名称已存在"},{status:400});let v=await d.A.findByIdAndUpdate(s,{$set:y},{new:!0,runValidators:!0}).select("-submittedBy -reviewNotes -reviewedBy");return o.NextResponse.json({success:!0,data:v,message:"工具更新成功"})}catch(e){if(console.error("Error updating tool:",e),"ValidationError"===e.name){let t=Object.values(e.errors).map(e=>e.message);return o.NextResponse.json({success:!1,error:"验证失败",details:t},{status:400})}return o.NextResponse.json({success:!1,error:"更新工具失败"},{status:500})}}async function f(e,{params:t}){try{await (0,u.A)();let{id:e}=t;if(!g().Types.ObjectId.isValid(e))return o.NextResponse.json({success:!1,error:"无效的工具ID"},{status:400});if(!await d.A.findByIdAndDelete(e))return o.NextResponse.json({success:!1,error:"工具不存在"},{status:404});return o.NextResponse.json({success:!0,message:"工具删除成功"})}catch(e){return console.error("Error deleting tool:",e),o.NextResponse.json({success:!1,error:"删除工具失败"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tools/[id]/route",pathname:"/api/tools/[id]",filename:"route",bundlePath:"app/api/tools/[id]/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:w,serverHooks:S}=x;function A(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:w})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(56037),i=r.n(s);let n=new s.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({launchDate:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let a=i().models.Tool||i().model("Tool",n)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),i=r.n(s);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=i().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,4999,3136],()=>r(18508));module.exports=s})();