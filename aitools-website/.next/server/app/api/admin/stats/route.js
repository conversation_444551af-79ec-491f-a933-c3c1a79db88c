(()=>{var e={};e.id=9759,e.ids=[9759],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(56037),a=r.n(i);let n=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({launchDate:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let s=a().models.Tool||a().model("Tool",n)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>l});var i={};r.r(i),r.d(i,{GET:()=>c});var a=r(96559),n=r(48088),s=r(37719),o=r(32190),d=r(75745),u=r(30762);async function c(e){try{let t;await (0,d.A)();let{searchParams:r}=new URL(e.url),i=r.get("timeRange")||"7d",a=new Date;switch(i){case"1d":t=new Date(a.getTime()-864e5);break;case"7d":default:t=new Date(a.getTime()-6048e5);break;case"30d":t=new Date(a.getTime()-2592e6);break;case"90d":t=new Date(a.getTime()-7776e6)}let[n,s,c,p,m,l,g,v,y]=await Promise.all([u.A.countDocuments(),u.A.countDocuments({status:"pending"}),u.A.countDocuments({status:"approved"}),u.A.countDocuments({status:"rejected"}),u.A.aggregate([{$group:{_id:null,total:{$sum:"$views"}}}]),u.A.aggregate([{$group:{_id:null,total:{$sum:"$likes"}}}]),u.A.countDocuments({submittedAt:{$gte:t}}),u.A.countDocuments({status:"approved",reviewedAt:{$gte:t}}),u.A.countDocuments({status:"rejected",reviewedAt:{$gte:t}})]),w=await u.A.aggregate([{$match:{status:"approved"}},{$group:{_id:"$category",count:{$sum:1},totalViews:{$sum:"$views"},totalLikes:{$sum:"$likes"}}},{$sort:{count:-1}}]),x=await u.A.find({status:"approved"}).sort({views:-1}).limit(10).select("name category views likes").lean(),h=await u.A.find({$or:[{submittedAt:{$gte:t}},{reviewedAt:{$gte:t}}]}).sort({updatedAt:-1}).limit(20).select("name status submittedAt reviewedAt submittedBy reviewedBy").lean(),A=await u.A.aggregate([{$match:{submittedAt:{$gte:new Date(a.getTime()-6048e5)}}},{$group:{_id:{date:{$dateToString:{format:"%Y-%m-%d",date:"$submittedAt"}},status:"$status"},count:{$sum:1}}},{$sort:{"_id.date":1}}]),$=[];for(let e=6;e>=0;e--){let t=new Date(a.getTime()-24*e*36e5),r=t.toISOString().split("T")[0],i=t.toLocaleDateString("zh-CN",{weekday:"short"}),n=A.filter(e=>e._id.date===r),s=n.find(e=>"pending"===e._id.status)?.count||0,o=n.find(e=>"approved"===e._id.status)?.count||0,d=n.find(e=>"rejected"===e._id.status)?.count||0;$.push({date:r,day:i,submissions:s,approvals:o,rejections:d})}let f=await u.A.aggregate([{$match:{status:{$in:["approved","rejected"]},reviewedAt:{$exists:!0},submittedAt:{$exists:!0}}},{$project:{reviewTime:{$divide:[{$subtract:["$reviewedAt","$submittedAt"]},36e5]}}},{$group:{_id:null,avgReviewTime:{$avg:"$reviewTime"}}}]),D={totalTools:n,pendingTools:s,approvedTools:c,rejectedTools:p,totalViews:m[0]?.total||0,totalLikes:l[0]?.total||0,recentSubmissions:g,recentApprovals:v,recentRejections:y,avgReviewTime:f[0]?.avgReviewTime||0};return o.NextResponse.json({success:!0,data:{overview:D,categoryStats:w,topTools:x,recentActivity:h,dailyStats:$,timeRange:i}})}catch(e){return console.error("Error fetching admin stats:",e),o.NextResponse.json({success:!1,error:"获取统计数据失败"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:l,serverHooks:g}=p;function v(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:l})}},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),a=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let o=async function(){if(s.conn)return s.conn;s.promise||(s.promise=a().connect(n,{bufferCommands:!1}).then(e=>e));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580],()=>r(51639));module.exports=i})();