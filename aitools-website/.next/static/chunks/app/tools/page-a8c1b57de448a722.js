(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3554],{1324:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(5155),r=s(2115),l=s(4478),c=s(7797),n=s(2731),i=s(9783),d=s(5731),o=s(3467),x=s(7924),h=s(6932),u=s(6474),m=s(4653),g=s(5968);let p=[{value:"",label:"所有分类"},{value:"text-generation",label:"文本生成"},{value:"image-generation",label:"图像生成"},{value:"code-generation",label:"代码生成"},{value:"data-analysis",label:"数据分析"},{value:"audio-processing",label:"音频处理"},{value:"video-editing",label:"视频编辑"}],b=o.v4,y=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function j(){var e;let[t,s]=(0,r.useState)([]),[o,j]=(0,r.useState)(!0),[v,f]=(0,r.useState)(""),[N,w]=(0,r.useState)(""),[A,k]=(0,r.useState)(""),[S,T]=(0,r.useState)(""),[C,q]=(0,r.useState)("popular"),[L,E]=(0,r.useState)("grid"),[M,O]=(0,r.useState)(!1);(0,r.useEffect)(()=>{P()},[]);let P=async()=>{try{j(!0),f("");let e=await d.u.getTools({status:"published",limit:100});e.success&&e.data&&s(e.data.tools)}catch(e){f("获取工具列表失败，请稍后重试"),console.error("Error fetching tools:",e)}finally{j(!1)}},_=[...t.filter(e=>{let t=e.name.toLowerCase().includes(N.toLowerCase())||e.description.toLowerCase().includes(N.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(N.toLowerCase())),s=!A||e.category===A,a=!S||e.pricing===S;return t&&s&&a})].sort((e,t)=>{switch(C){case"popular":return(t.likes||0)-(e.likes||0);case"views":return(t.views||0)-(e.views||0);case"name":return e.name.localeCompare(t.name);case"newest":if(e.launchDate&&t.launchDate)return new Date(t.launchDate).getTime()-new Date(e.launchDate).getTime();return new Date(t.createdAt||"").getTime()-new Date(e.createdAt||"").getTime();default:return 0}});return o?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,a.jsx)(n.A,{size:"lg"})})})}):v?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(i.A,{message:v})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"AI 工具目录"}),(0,a.jsxs)("p",{className:"text-lg text-gray-600",children:["发现 ",t.length," 个精选的 AI 工具，提升您的工作效率"]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或标签...",value:N,onChange:e=>w(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(x.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"md:hidden mb-4",children:(0,a.jsxs)("button",{onClick:()=>O(!M),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,a.jsx)(u.A,{className:"ml-2 h-4 w-4 transform ".concat(M?"rotate-180":"")})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 ".concat(M?"block":"hidden md:grid"),children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"分类"}),(0,a.jsx)("select",{value:A,onChange:e=>k(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:p.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,a.jsx)("select",{value:S,onChange:e=>T(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:b.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,a.jsx)("select",{value:C,onChange:e=>q(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:y.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,a.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,a.jsx)("button",{onClick:()=>E("grid"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ".concat("grid"===L?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,a.jsx)(m.A,{className:"h-4 w-4 mx-auto"})}),(0,a.jsx)("button",{onClick:()=>E("list"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ".concat("list"===L?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,a.jsx)(g.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["显示 ",_.length," 个结果",N&&' 搜索 "'.concat(N,'"'),A&&' 在 "'.concat(null==(e=p.find(e=>e.value===A))?void 0:e.label,'"')]})}),_.length>0?(0,a.jsx)("div",{className:"grid"===L?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:_.map(e=>(0,a.jsx)(c.default,{tool:e},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(x.A,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,a.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]})]})})}},2731:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(5155);function r(e){let{size:t="md",className:s=""}=e;return(0,a.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4478:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(5155);s(2115);var r=s(6874),l=s.n(r),c=s(365);let n=e=>{let{children:t}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(c.default,{}),(0,a.jsx)("main",{className:"flex-1",children:t}),(0,a.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,a.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4529:(e,t,s)=>{Promise.resolve().then(s.bind(s,1324))},4653:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},5339:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5731:(e,t,s)=>{"use strict";s.d(t,{u:()=>l});let a=s(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class r{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...t.headers},...t},r=await fetch(s,a),l=await r.json();if(!r.ok)throw Error(l.error||"HTTP error! status: ".concat(r.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=a){this.baseURL=e}}let l=new r},5968:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6474:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6932:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9783:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(5155),r=s(5339),l=s(4416);function c(e){let{message:t,onClose:s,className:c=""}=e;return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(c),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-red-800 text-sm",children:t})}),s&&(0,a.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,2313,3254,8441,1684,7358],()=>t(4529)),_N_E=e.O()}]);