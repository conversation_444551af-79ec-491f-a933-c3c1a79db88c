(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8664],{365:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});var a=s(2115);function r(){return(0,a.useEffect)(()=>{let e={},t=(e,t)=>{window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t),non_interaction:!0})};if("undefined"!=typeof PerformanceObserver){let s;new PerformanceObserver(s=>{let a=s.getEntries().find(e=>"first-contentful-paint"===e.name);a&&(e.fcp=a.startTime,t("FCP",a.startTime))}).observe({entryTypes:["paint"]}),new PerformanceObserver(s=>{let a=s.getEntries(),r=a[a.length-1];e.lcp=r.startTime,t("LCP",r.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(s=>{s.getEntries().forEach(s=>{e.fid=s.processingStart-s.startTime,t("FID",s.processingStart-s.startTime)})}).observe({entryTypes:["first-input"]}),s=0,new PerformanceObserver(a=>{a.getEntries().forEach(e=>{e.hadRecentInput||(s+=e.value)}),e.cls=s,t("CLS",s)}).observe({entryTypes:["layout-shift"]})}let s=performance.getEntriesByType("navigation")[0];if(s){let a=s.responseStart-s.requestStart;e.ttfb=a,t("TTFB",a)}let a=()=>{Object.keys(e).length>0&&console.log("Final Performance Metrics:",e)};return window.addEventListener("beforeunload",a),()=>{window.removeEventListener("beforeunload",a)}},[]),null}},2984:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,365)),Promise.resolve().then(s.bind(s,6401))},3467:(e,t,s)=>{"use strict";s.d(t,{$g:()=>d,Ef:()=>c,Y$:()=>i,kX:()=>a,mV:()=>o,tF:()=>m,v4:()=>n,vS:()=>r});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},r=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],i=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],c=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4601:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(5155),r=s(2115),l=s(2108),n=s(9911);function i(e){let{toolId:t,initialLikes:s=0,initialLiked:i=!1,onLoginRequired:c,onUnlike:o,isInLikedPage:d=!1}=e,{data:m}=(0,l.useSession)(),[u,x]=(0,r.useState)(i),[h,g]=(0,r.useState)(s),[p,b]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/tools/".concat(t,"/like"));if(e.ok){let t=await e.json();t.success&&(x(t.data.liked),g(t.data.likes))}}catch(e){console.error("Failed to fetch like status:",e)}};m&&e()},[t,m]);let f=async()=>{if(!m){null==c||c();return}if(!p){b(!0);try{let e=await fetch("/api/tools/".concat(t,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d?{forceUnlike:!0}:{})});if(e.ok){let s=await e.json();if(s.success){let e=s.data.liked;x(e),g(s.data.likes),!e&&o&&o(t)}}else{let t=await e.json();console.error("Like failed:",t.message)}}catch(e){console.error("Like request failed:",e)}finally{b(!1)}}};return(0,a.jsxs)("button",{onClick:f,disabled:p,className:"\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ".concat(u?"bg-red-50 text-red-600 hover:bg-red-100":"bg-gray-50 text-gray-600 hover:bg-gray-100","\n        ").concat(p?"opacity-50 cursor-not-allowed":"hover:scale-105","\n        border border-gray-200 hover:border-gray-300\n      "),children:[u?(0,a.jsx)(n.Mbv,{className:"w-4 h-4 text-red-500"}):(0,a.jsx)(n.sOK,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:h>0?h:""})]})}},5731:(e,t,s)=>{"use strict";s.d(t,{u:()=>l});let a=s(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class r{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...t.headers},...t},r=await fetch(s,a),l=await r.json();if(!r.ok)throw Error(l.error||"HTTP error! status: ".concat(r.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=a){this.baseURL=e}}let l=new r},6063:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(5155),r=s(2115),l=s(2108),n=s(9911);function i(e){let{isOpen:t,onClose:s}=e,[i,c]=(0,r.useState)("method"),[o,d]=(0,r.useState)(""),[m,u]=(0,r.useState)(""),[x,h]=(0,r.useState)(!1),[g,p]=(0,r.useState)(""),b=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",s=document.createElement("div");s.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},f=()=>{c("method"),d(""),u(""),p(""),s()},y=async e=>{try{h(!0),await (0,l.signIn)(e,{callbackUrl:"/"})}catch(e){b("登录失败，请稍后重试","error")}finally{h(!1)}},j=async()=>{if(!o)return void p("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o))return void p("请输入有效的邮箱地址");p(""),h(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o})}),t=await e.json();t.success?(u(t.token),c("code"),b("验证码已发送，请查看您的邮箱")):b(t.error||"发送失败，请稍后重试","error")}catch(e){b("网络错误，请检查网络连接","error")}finally{h(!1)}},v=async e=>{if(6===e.length){h(!0);try{let t=await (0,l.signIn)("email-code",{email:o,code:e,token:m,redirect:!1});(null==t?void 0:t.ok)?(b("登录成功，欢迎回来！"),f()):b((null==t?void 0:t.error)||"验证码错误","error")}catch(e){b("网络错误，请检查网络连接","error")}finally{h(!1)}}},N=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");if(s[e].value=t,t&&e<5){var a;null==(a=s[e+1])||a.focus()}let r=Array.from(s).map(e=>e.value).join("");6===r.length&&v(r)};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:f}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===i&&"登录 AI Tools Directory","email"===i&&"邮箱登录","code"===i&&"输入验证码"]}),(0,a.jsx)("button",{onClick:f,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(n.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:["method"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>y("google"),disabled:x,children:[(0,a.jsx)(n.DSS,{}),"使用 Google 登录"]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>y("github"),disabled:x,children:[(0,a.jsx)(n.hL4,{}),"使用 GitHub 登录"]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>c("email"),children:[(0,a.jsx)(n.maD,{}),"使用邮箱登录"]})]}),"email"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,a.jsx)("input",{type:"email",value:o,onChange:e=>d(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&j(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),g&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:j,disabled:x,children:x?"发送中...":"发送验证码"}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]}),"code"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",o," 的6位验证码"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,a.jsx)("input",{type:"text",maxLength:1,onChange:t=>N(e,t.target.value),disabled:x,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("email"),children:"重新发送验证码"}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]})]})]})]}):null}},6401:(e,t,s)=>{"use strict";s.d(t,{default:()=>j});var a=s(5155),r=s(2115),l=s(6874),n=s.n(l),i=s(4601),c=s(2108),o=s(9911);function d(e){let{toolId:t,onLoginRequired:s}=e,{data:l}=(0,c.useSession)(),[n,i]=(0,r.useState)([]),[d,m]=(0,r.useState)(""),[u,x]=(0,r.useState)(null),[h,g]=(0,r.useState)(""),[p,b]=(0,r.useState)(!1),[f,y]=(0,r.useState)(!1),j=async()=>{b(!0);try{let e=await fetch("/api/tools/".concat(t,"/comments"));if(e.ok){let t=await e.json();t.success&&i(t.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}finally{b(!1)}};(0,r.useEffect)(()=>{j()},[t]);let v=async()=>{if(!l){null==s||s();return}if(d.trim()){y(!0);try{let e=await fetch("/api/tools/".concat(t,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:d.trim()})});if(e.ok)(await e.json()).success&&(m(""),j());else{let t=await e.json();console.error("Comment submission failed:",t.message)}}catch(e){console.error("Comment submission error:",e)}finally{y(!1)}}},N=async e=>{if(!l){null==s||s();return}if(h.trim()){y(!0);try{let s=await fetch("/api/tools/".concat(t,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:h.trim(),parentId:e})});if(s.ok)(await s.json()).success&&(g(""),x(null),j());else{let e=await s.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{y(!1)}}},w=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/36e5);return s<1?"刚刚":s<24?"".concat(s,"小时前"):s<168?"".concat(Math.floor(s/24),"天前"):t.toLocaleDateString("zh-CN")},E=e=>{let{comment:t,isReply:s=!1}=e;return(0,a.jsx)("div",{className:"".concat(s?"ml-8 border-l-2 border-gray-100 pl-4":""),children:(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:t.userId.image?(0,a.jsx)("img",{src:t.userId.image,alt:t.userId.name,className:"w-8 h-8 rounded-full"}):(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)(o.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:t.userId.name}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:w(t.createdAt)})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-2",children:t.content}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:!s&&(0,a.jsxs)("button",{onClick:()=>x(u===t._id?null:t._id),className:"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1",children:[(0,a.jsx)(o.w1Z,{className:"w-3 h-3"}),"回复"]})}),u===t._id&&(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("textarea",{value:h,onChange:e=>g(e.target.value),placeholder:"写下你的回复...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,maxLength:1e3}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-2",children:[(0,a.jsx)("button",{onClick:()=>{x(null),g("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"取消"}),(0,a.jsx)("button",{onClick:()=>N(t._id),disabled:f||!h.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"发送中...":"发送"})]})]}),t.replies&&t.replies.length>0&&(0,a.jsx)("div",{className:"mt-4 space-y-4",children:t.replies.map(e=>(0,a.jsx)(E,{comment:e,isReply:!0},e._id))})]})]})})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["评论 (",n.length,")"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:d,onChange:e=>m(e.target.value),placeholder:l?"写下你的评论...":"请先登录后评论",className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!l}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[d.length,"/1000"]}),(0,a.jsx)("button",{onClick:v,disabled:f||!d.trim()||!l,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"发送中...":"发表评论"})]})]}),p?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"加载评论中..."})]}):0===n.length?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"暂无评论，来发表第一条评论吧！"})}):(0,a.jsx)("div",{className:"space-y-6",children:n.map(e=>(0,a.jsx)(E,{comment:e},e._id))})]})}var m=s(6063),u=s(5731),x=s(3467),h=s(5868),g=s(2657),p=s(1976),b=s(6516),f=s(3332),y=s(3786);function j(e){let{initialTool:t,toolId:s}=e,[l,c]=(0,r.useState)(t),[o,j]=(0,r.useState)([]),[v,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{w(l.category)},[l.category]);let w=async e=>{try{let t=await u.u.getTools({category:e,status:"published",limit:3});if(t.success&&t.data){let e=t.data.tools.filter(e=>e._id!==s);j(e.slice(0,3))}}catch(e){}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("article",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsxs)("header",{className:"flex items-start justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[l.logo?(0,a.jsx)("img",{src:l.logo,alt:"".concat(l.name," logo"),className:"w-16 h-16 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:l.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:l.name}),l.tagline&&(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-3",children:l.tagline}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat((0,x.Ef)(l.pricing)),children:[(0,a.jsx)(h.A,{className:"mr-1 h-4 w-4"}),(0,x.mV)(l.pricing)]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[l.views||0," 浏览"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[l.likes||0," 喜欢"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{toolId:l._id,initialLikes:l.likes,onLoginRequired:()=>N(!0)}),(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-500 transition-colors",children:(0,a.jsx)(b.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed",children:l.description})}),l.tags&&l.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:l.tags.map((e,t)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",children:[(0,a.jsx)(f.A,{className:"mr-1 h-3 w-3"}),e]},t))}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("a",{href:l.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(y.A,{className:"mr-2 h-5 w-5"}),"访问 ",l.name]}),(0,a.jsx)(i.A,{toolId:l._id,initialLikes:l.likes,onLoginRequired:()=>N(!0)})]})]})}),(0,a.jsxs)("aside",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"工具信息"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"分类"}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:l.category})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"价格模式"}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat((0,x.Ef)(l.pricing)),children:(0,x.mV)(l.pricing)})]}),l.publishedAt&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"发布日期"}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:new Date(l.publishedAt).toLocaleDateString("zh-CN")})]})]})]}),o.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"相关工具"}),(0,a.jsx)("div",{className:"space-y-4",children:o.map(e=>(0,a.jsx)("div",{children:(0,a.jsx)(n(),{href:"/tools/".concat(e._id),className:"block p-3 rounded-lg hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,a.jsx)("img",{src:e.logo,alt:e.name,className:"w-10 h-10 rounded object-cover"}):(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:e.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 mt-1",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded ".concat((0,x.Ef)(e.pricing)),children:(0,x.mV)(e.pricing)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{children:[e.views||0," 浏览"]}),(0,a.jsxs)("span",{children:[e.likes||0," 喜欢"]})]})]})]})]})})},e._id))})]})]})]}),(0,a.jsx)("div",{className:"mt-12",children:(0,a.jsx)(d,{toolId:l._id,onLoginRequired:()=>N(!0)})}),(0,a.jsx)(m.A,{isOpen:v,onClose:()=>N(!1)})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,9167,8441,1684,7358],()=>t(2984)),_N_E=e.O()}]);