import mongoose, { Document, Schema } from 'mongoose';

export interface ITool extends Document {
  name: string;
  tagline?: string; // 工具标语/副标题
  description: string;
  longDescription?: string;
  website: string;
  logo?: string;
  category: string;
  tags: string[];
  pricing: 'free' | 'freemium' | 'paid';
  pricingDetails?: string;
  screenshots?: string[];
  submittedBy: string; // User ID who submitted
  submittedAt: Date;
  launchDate?: Date; // 实际发布日期，一般等于selectedLaunchDate
  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态
  reviewNotes?: string;
  reviewedBy?: string; // Admin ID who reviewed
  reviewedAt?: Date;

  // 发布日期选择相关
  launchDateSelected?: boolean; // 是否已选择发布日期
  selectedLaunchDate?: Date; // 用户选择的发布日期
  launchOption?: 'free' | 'paid'; // 发布选项：免费或付费

  // 付费相关
  paymentRequired?: boolean; // 是否需要付费
  paymentAmount?: number; // 付费金额（分为单位）
  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'; // 支付状态
  orderId?: string; // 订单ID
  paymentMethod?: string; // 支付方式
  paidAt?: Date; // 支付完成时间

  views: number;
  likes: number;
  likedBy: string[]; // 点赞用户ID列表
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ToolSchema: Schema = new Schema({
  name: {
    type: String,
    required: [true, 'Tool name is required'],
    trim: true,
    maxlength: [100, 'Tool name cannot exceed 100 characters']
  },
  tagline: {
    type: String,
    trim: true,
    maxlength: [200, 'Tagline cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Tool description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  longDescription: {
    type: String,
    trim: true,
    maxlength: [2000, 'Long description cannot exceed 2000 characters']
  },
  website: {
    type: String,
    required: [true, 'Website URL is required'],
    trim: true,
    validate: {
      validator: function(v: string) {
        return /^https?:\/\/.+/.test(v);
      },
      message: 'Please enter a valid URL'
    }
  },
  logo: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'text-generation',
      'image-generation', 
      'video-generation',
      'audio-generation',
      'code-generation',
      'data-analysis',
      'productivity',
      'design',
      'marketing',
      'education',
      'research',
      'other'
    ]
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  pricing: {
    type: String,
    required: [true, 'Pricing model is required'],
    enum: ['free', 'freemium', 'paid']
  },
  pricingDetails: {
    type: String,
    trim: true,
    maxlength: [500, 'Pricing details cannot exceed 500 characters']
  },
  screenshots: [{
    type: String,
    trim: true
  }],
  submittedBy: {
    type: String,
    required: [true, 'Submitter ID is required'],
    trim: true
  },
  submittedAt: {
    type: Date,
    default: Date.now
  },
  launchDate: {
    type: Date
  },
  status: {
    type: String,
    required: true,
    enum: ['draft', 'pending', 'approved', 'rejected'],
    default: 'draft'
  },
  reviewNotes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Review notes cannot exceed 1000 characters']
  },
  reviewedBy: {
    type: String,
    trim: true
  },
  reviewedAt: {
    type: Date
  },

  // 发布日期选择相关
  launchDateSelected: {
    type: Boolean,
    default: false
  },
  selectedLaunchDate: {
    type: Date
  },
  launchOption: {
    type: String,
    enum: ['free', 'paid']
  },

  // 付费相关
  paymentRequired: {
    type: Boolean,
    default: false
  },
  paymentAmount: {
    type: Number,
    min: 0
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded']
  },
  orderId: {
    type: String,
    trim: true
  },
  paymentMethod: {
    type: String,
    trim: true
  },
  paidAt: {
    type: Date
  },
  views: {
    type: Number,
    default: 0,
    min: 0
  },
  likes: {
    type: Number,
    default: 0,
    min: 0
  },
  likedBy: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
ToolSchema.index({ status: 1, isActive: 1 });
ToolSchema.index({ category: 1, status: 1 });
ToolSchema.index({ tags: 1, status: 1 });
ToolSchema.index({ submittedBy: 1 });
ToolSchema.index({ launchDate: -1 });
ToolSchema.index({ views: -1 });
ToolSchema.index({ likes: -1 });

// Text search index
ToolSchema.index({
  name: 'text',
  tagline: 'text',
  description: 'text',
  longDescription: 'text',
  tags: 'text'
});

export default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);
