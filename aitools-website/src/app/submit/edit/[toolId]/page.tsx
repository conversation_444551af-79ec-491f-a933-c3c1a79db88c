'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import LoginModal from '@/components/auth/LoginModal';
import { Tool } from '@/lib/api';
import { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';
import {
  ArrowLeft,
  Upload
} from 'lucide-react';
import Link from 'next/link';
import { MAX_TAGS_COUNT } from '@/constants/tags';
import TagSelector from '@/components/TagSelector';

interface FormData {
  name: string;
  tagline: string;
  description: string;
  website: string;
  logo: string;
  category: string;
  tags: string[];
  pricing: string;
}

export default function EditToolPage({ params }: { params: Promise<{ toolId: string }> }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [toolId, setToolId] = useState<string>('');
  const [tool, setTool] = useState<Tool | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [submitMessage, setSubmitMessage] = useState('');

  const [logoUrl, setLogoUrl] = useState<string>('');
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    tagline: '',
    description: '',
    website: '',
    logo: '',
    category: '',
    tags: [],
    pricing: ''
  });

  // 获取工具ID
  useEffect(() => {
    params.then(p => setToolId(p.toolId));
  }, [params]);

  // 获取工具信息
  useEffect(() => {
    if (!toolId) return;

    const fetchToolInfo = async () => {
      try {
        const response = await fetch(`/api/tools/${toolId}`);
        const data = await response.json();

        if (data.success) {
          const toolData = data.data;
          setTool(toolData);
          
          // 填充表单数据
          setFormData({
            name: toolData.name || '',
            tagline: toolData.tagline || '',
            description: toolData.description || '',
            website: toolData.website || '',
            logo: toolData.logo || '',
            category: toolData.category || '',
            tags: toolData.tags || [],
            pricing: toolData.pricing || ''
          });
          
          setLogoUrl(toolData.logo || '');
        } else {
          setSubmitStatus('error');
          setSubmitMessage(data.message || '获取工具信息失败');
        }
      } catch (error) {
        console.error('获取工具信息失败:', error);
        setSubmitStatus('error');
        setSubmitMessage('网络错误，请重试');
      } finally {
        setLoading(false);
      }
    };

    if (session) {
      fetchToolInfo();
    } else if (status !== 'loading') {
      setLoading(false);
    }
  }, [toolId, session, status]);

  // 检查用户权限
  useEffect(() => {
    if (tool && session?.user?.email) {
      // 这里需要检查用户是否有权限编辑这个工具
      // 暂时跳过权限检查，后续可以添加
    }
  }, [tool, session]);

  const categories = [
    { value: 'text-generation', label: '文本生成' },
    { value: 'image-generation', label: '图像生成' },
    { value: 'code-generation', label: '代码生成' },
    { value: 'data-analysis', label: '数据分析' },
    { value: 'audio-processing', label: '音频处理' },
    { value: 'video-editing', label: '视频编辑' },
    { value: 'design-tools', label: '设计工具' },
    { value: 'productivity', label: '生产力工具' },
    { value: 'customer-service', label: '客户服务' }
  ];

  // 使用统一的价格选项配置
  const pricingOptions = TOOL_PRICING_FORM_OPTIONS;



  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = '工具名称是必填项';
    if (!formData.description.trim()) newErrors.description = '工具描述是必填项';
    if (!formData.website.trim()) newErrors.website = '官方网站是必填项';
    if (!formData.category) newErrors.category = '请选择一个分类';
    if (!formData.pricing) newErrors.pricing = '请选择价格模式';

    // URL validation
    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      newErrors.website = '请输入有效的网站地址（以 http:// 或 https:// 开头）';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 检查用户是否已登录
    if (!session) {
      setIsLoginModalOpen(true);
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // 调用更新API
      const response = await fetch(`/api/tools/${toolId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          tagline: formData.tagline,
          description: formData.description,
          website: formData.website,
          logo: logoUrl || undefined,
          category: formData.category,
          tags: formData.tags,
          pricing: formData.pricing
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSubmitStatus('success');
        setSubmitMessage('工具信息更新成功！');
        // 3秒后跳转回提交的工具列表
        setTimeout(() => {
          router.push('/profile/submitted');
        }, 2000);
      } else {
        setSubmitStatus('error');
        setSubmitMessage(data.error || '更新失败，请重试');
      }
    } catch (error) {
      console.error('更新工具失败:', error);
      setSubmitStatus('error');
      setSubmitMessage('网络错误，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogoUpload = async (file: File) => {
    if (!file) return;

    setUploadingLogo(true);
    const formData = new FormData();
    formData.append('logo', file);

    try {
      const response = await fetch('/api/upload/logo', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        setLogoUrl(data.data.url);
        setFormData(prev => ({ ...prev, logo: data.data.url }));
      } else {
        setSubmitStatus('error');
        setSubmitMessage(data.message || '上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      setSubmitStatus('error');
      setSubmitMessage('上传失败，请重试');
    } finally {
      setUploadingLogo(false);
    }
  };





  if (status === 'loading' || loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </Layout>
    );
  }

  if (!session) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">请先登录</h1>
            <p className="text-gray-600 mb-6">您需要登录后才能编辑工具信息</p>
            <button
              onClick={() => setIsLoginModalOpen(true)}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              登录
            </button>
          </div>
        </div>
        <LoginModal
          isOpen={isLoginModalOpen}
          onClose={() => setIsLoginModalOpen(false)}
        />
      </Layout>
    );
  }

  if (!tool) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">工具不存在</h1>
            <p className="text-gray-600 mb-6">您要编辑的工具不存在或已被删除</p>
            <Link
              href="/profile/submitted"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              返回工具列表
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/profile/submitted"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回工具列表
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">编辑工具信息</h1>
          <p className="text-gray-600 mt-2">
            更新您的工具信息，让更多用户了解您的产品
          </p>
        </div>

        {/* Status Messages */}
        {submitStatus === 'success' && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800">{submitMessage}</p>
          </div>
        )}

        {submitStatus === 'error' && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{submitMessage}</p>
          </div>
        )}

        {/* 编辑权限提示 */}
        {tool && (
          <div className="mb-6 p-4 rounded-lg border">
            {tool.status === 'draft' && (
              <div className="bg-gray-50 border-gray-200">
                <p className="text-sm text-gray-700">
                  <span className="font-medium">草稿状态：</span>可以编辑所有信息
                </p>
              </div>
            )}
            {tool.status === 'pending' && (
              <div className="bg-yellow-50 border-yellow-200">
                <p className="text-sm text-yellow-700">
                  <span className="font-medium">审核中：</span>可以编辑所有信息，但建议谨慎修改
                </p>
              </div>
            )}
            {tool.status === 'approved' && (
              <div className="bg-blue-50 border-blue-200">
                <p className="text-sm text-blue-700">
                  <span className="font-medium">已通过审核：</span>可以编辑基础信息，但不能修改网站地址、分类、价格模式和标签
                </p>
              </div>
            )}
            {tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date() && (
              <div className="bg-green-50 border-green-200">
                <p className="text-sm text-green-700">
                  <span className="font-medium">已发布：</span>只能编辑名称、简介、描述等基础信息
                </p>
              </div>
            )}
            {tool.status === 'rejected' && (
              <div className="bg-red-50 border-red-200">
                <p className="text-sm text-red-700">
                  <span className="font-medium">审核被拒：</span>可以编辑所有信息后重新提交
                </p>
              </div>
            )}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {/* Basic Information */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">基本信息</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  工具名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="例如：ChatGPT"
                />
                {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  工具标语
                </label>
                <input
                  type="text"
                  value={formData.tagline}
                  onChange={(e) => setFormData(prev => ({ ...prev, tagline: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="例如：AI驱动的对话助手"
                />
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                工具描述 *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="简要描述您的工具功能和特点..."
              />
              {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
            </div>



            {/* 网站URL - 只在特定状态下可编辑 */}
            {['draft', 'pending', 'rejected'].includes(tool?.status || '') && (
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  官方网站 *
                </label>
                <input
                  type="url"
                  value={formData.website}
                  onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.website ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="https://example.com"
                />
                {errors.website && <p className="text-red-600 text-sm mt-1">{errors.website}</p>}
              </div>
            )}

            {/* 网站URL - 只读显示 */}
            {['approved', 'published'].includes(tool?.status || '') && (
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  官方网站
                </label>
                <div className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                  {formData.website}
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {tool?.status === 'approved' ? '审核通过后不可修改网站地址' : '已发布工具不可修改网站地址'}
                </p>
              </div>
            )}
          </div>

          {/* Logo Upload */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">工具Logo</h2>

            <div className="flex items-start space-x-6">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  上传Logo图片
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        handleLogoUpload(file);
                      }
                    }}
                    className="hidden"
                    id="logo-upload"
                  />
                  <label htmlFor="logo-upload" className="cursor-pointer">
                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">
                      {uploadingLogo ? '上传中...' : '点击上传或拖拽图片到此处'}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      支持 PNG, JPG, GIF 格式，建议尺寸 200x200px
                    </p>
                  </label>
                </div>
              </div>

              {logoUrl && (
                <div className="w-24 h-24 border border-gray-300 rounded-lg overflow-hidden">
                  <img
                    src={logoUrl}
                    alt="Logo预览"
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Category and Pricing */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">分类和定价</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 分类 - 只在特定状态下可编辑 */}
              {['draft', 'pending', 'rejected'].includes(tool?.status || '') ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    工具分类 *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.category ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    <option value="">请选择分类</option>
                    {categories.map(category => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                  {errors.category && <p className="text-red-600 text-sm mt-1">{errors.category}</p>}
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    工具分类
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                    {categories.find(cat => cat.value === formData.category)?.label || formData.category}
                  </div>
                  <p className="text-sm text-gray-500 mt-1">分类不可修改</p>
                </div>
              )}

              {/* 价格模式 - 只在特定状态下可编辑 */}
              {['draft', 'pending', 'rejected'].includes(tool?.status || '') ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    价格模式 *
                  </label>
                  <select
                    value={formData.pricing}
                    onChange={(e) => setFormData(prev => ({ ...prev, pricing: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.pricing ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    <option value="">请选择价格模式</option>
                    {pricingOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  {errors.pricing && <p className="text-red-600 text-sm mt-1">{errors.pricing}</p>}
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    价格模式
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                    {pricingOptions.find(opt => opt.value === formData.pricing)?.label || formData.pricing}
                  </div>
                  <p className="text-sm text-gray-500 mt-1">价格模式不可修改</p>
                </div>
              )}
            </div>


          </div>

          {/* Tags */}
          {['draft', 'pending', 'rejected'].includes(tool?.status || '') ? (
            <div className="mb-8">
              <TagSelector
                selectedTags={formData.tags}
                onTagsChange={(tags) => setFormData(prev => ({ ...prev, tags }))}
                maxTags={MAX_TAGS_COUNT}
              />
            </div>
          ) : (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">标签</h2>
              <p className="text-sm text-gray-500 mb-4">标签不可修改</p>
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}



          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                isSubmitting
                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" className="mr-2" />
                  更新中...
                </div>
              ) : (
                '更新工具信息'
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </Layout>
  );
}
